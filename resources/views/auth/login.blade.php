<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Sign In with EVOLUTION888 ID</title>
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Подключаем стили, например, ваш основной CSS -->
    <link href="css/techbetatemplates.webflow.14600f26e.css?v=0.1" rel="stylesheet" type="text/css">

    <style>
        .hidden {
            display: none;
        }
        /* Стили для блока партнёрского кода */
        .partner-code-block {
            border: 2px dashed #800080;
            padding: 10px;
            margin-top: 15px;
            border-radius: 5px;
            background-color: #f9f4fc;
        }
        .partner-code-label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #800080;
        }
        /* Стили лейблов формы */
        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
    </style>

    <!-- Скрипт для сохранения referral code из URL в localStorage -->
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const urlParams = new URLSearchParams(window.location.search);
            const refCode = urlParams.get('ref');
            if (refCode) {
                localStorage.setItem('referral_code', refCode);
                // Очистить URL от параметра ref
                const cleanUrl = window.location.origin + window.location.pathname;
                window.history.replaceState({}, document.title, cleanUrl);
            }
        });
    </script>
</head>
<body>
    <div class="page-wrapper full-height-page">
        <!-- Шапка сайта (header) -->
			<div data-animation="default" data-collapse="all" data-duration="400" data-easing="ease" data-easing2="ease" role="banner" class="header-wrapper w-nav">
				<div class="container-default w-container">
					<div class="header-container-wrapper center" style="display: flex; justify-content: center; align-items: center; position: relative;">
						
						<!-- Логотип по центру -->
						<div class="logo-wrapper v1 dont-shirnk" style="z-index: 1;">
							<a href="/" class="logo-link w-inline-block">
								<img src="/images/evolution888_logo.png" style="height: 50px;" loading="eager" alt="Evolution888 Club">
							</a>
						</div>

						<!-- Селектор языка справа поверх (абсолютно позиционируется) -->
						<div class="language-selector-wrapper" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);">
							@include('partials.language-selector-simple')
						</div>

					</div>
				</div>
			</div>

        <!-- Основной контент -->
        <section class="section full-height v1">
            <div class="w-layout-blockcontainer container-default width-100 z-index-1 w-container">
                <div class="w-layout-grid grid-2-columns full-height-v1-grid">
                    <div class="inner-container _490px" style="margin-top: 100px;">
                        <div class="center-content---tablet">
                            <h1 class="display-9"><span class="title-tag">EVO</span> id</h1>
                            <div class="mg-top-small mg-top-16px---mbl">
                                <p>A single account for all EVOLUTION888 ecosystem services.
Authorization, login, and registration — the system will handle everything automatically.
Quick and easy signup. We wish you a productive experience!</p>
                            </div>
                            <div class="mg-top-default mg-top-24px---mbl width-100">
                                <div class="inner-container _380px _100-tablet">
                                    <div class="input-form-block-v1 w-form">
                                        <form id="auth-form" class="form-wrapper inside-input button-icon">
                                            @csrf
                                            <!-- Блок для ввода Email -->
                                            <div class="input-wrapper" id="email-block">
                                                <label for="email" class="form-label">Your Email</label>
                                                <input class="input button-inside icon-inside light w-input" maxlength="256" name="email" placeholder="Just enter your email" type="email" id="email" required>

                                                <!-- Блок для ввода партнёрского кода.
                                                     Изначально скрыт, JS покажет его, если в localStorage есть referral_code -->
                                                <div class="input-wrapper partner-code-block hidden" id="referral-code-block">
                                                    <label for="referral_code" class="partner-code-label">Referral Code</label>
                                                    <input class="input button-inside icon-inside light w-input" maxlength="256" name="referral_code" placeholder="If you were invited, enter the referral code here." type="text" id="referral_code" value="">
                                                </div>

                                                <!-- Кнопка "Получить код" -->
                                                <input type="submit" class="primary-button w-inline-block" style="margin-top: 10px; margin-left: auto; margin-right: auto;" id="get-code" value="Log In">
                                            </div>

                                            <!-- Блок для ввода кода из письма -->
                                            <div class="input-wrapper hidden" id="code-block">
                                                <input class="input button-inside icon-inside light w-input" minlength="6" maxlength="6" name="code" placeholder="Enter your code" type="text" id="code">
                                                <input type="button" data-wait="" class="primary-button inside-input button-icon w-button" id="login-code" value="">
                                            </div>

                                            <!-- Ссылки на смену Email и повторную отправку кода -->
                                            <div class="register-link hidden" id="actions-block" style="text-align:center; margin-top:10px;">
                                                <a href="#" id="change-email" style="color:#800080; text-decoration: underline; margin-right:20px;">Change Email</a>
                                                <a href="#" id="resend-code" style="color:#800080; text-decoration: underline;">Resend Code</a>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Блок с иллюстрациями (можно оставить без изменений) -->
                    <div class="image-container hero-image---v3">
                        <div class="hero-bg---v3"></div>
                        <div class="image-wrapper hero-image---v3">
                            <img src="images/66428c815343c98ba4928dee_the-notes-app-for-power-users-main-image-techbeta-x-webflow-template.png" loading="eager" alt="Illustration" class="image">
                        </div>
						<!--
                        <div class="image-wrapper hero-left-image---v3"> 
                            <img src="images/66428c81ad988558643cb3a4_the-notes-app-for-power-users-left-image-techbeta-x-webflow-template.png" loading="eager" alt="Illustration" class="image">
                        </div>
						
                        <div class="image-wrapper hero-right-image---v3">
                            <img src="images/66428c81e919fb33e543745a_the-notes-app-for-power-users-right-image-techbeta-x-webflow-template.png" loading="eager" alt="Illustration" class="image">
                        </div>
						-->
                    </div>
                </div>
            </div>
        </section>
    </div>
    
    <!-- Подключаем SweetAlert2 (через CDN) -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Скрипт для автоподстановки referral_code из localStorage -->
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const referralInput = document.getElementById('referral_code');
            if (referralInput) {
                const storedCode = localStorage.getItem('referral_code');
                if (storedCode) {
                    referralInput.value = storedCode;
                    document.getElementById('referral-code-block').classList.remove('hidden');
                }
            }
        });
    </script>

    <!-- Основной JS-код для авторизации -->
    <script>
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        const form = document.getElementById('auth-form');
        const emailInput = document.getElementById('email');
        const codeInput = document.getElementById('code');

        const emailBlock = document.getElementById('email-block');
        const codeBlock = document.getElementById('code-block');
        const actionsBlock = document.getElementById('actions-block');

        const getCodeBtn = document.getElementById('get-code');
        const loginCodeBtn = document.getElementById('login-code');
        const changeEmailLink = document.getElementById('change-email');
        const resendCodeLink = document.getElementById('resend-code');

        // Функция для отправки запросов
        async function sendRequest(url, data) {
            const res = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify(data)
            });
            return await res.json();
        }

        // Вывод сообщений через SweetAlert2
        function showMessage(msg, success = true) {
            if (!msg) return;
            Swal.fire({
                icon: success ? 'success' : 'error',
                text: msg,
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'OK'
            });
        }

        // Отправка email для получения кода
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = emailInput.value.trim();
            if (!email) {
                showMessage('Please enter your email.', false);
                return;
            }

            getCodeBtn.disabled = true;
            getCodeBtn.value = 'Sending Request...';

            try {
                const result = await sendRequest('{{ route("login.request_code") }}', { email: email });
                if (result.status === 'success') {
                    emailBlock.classList.add('hidden');
                    codeBlock.classList.remove('hidden');
                    actionsBlock.classList.remove('hidden');
                    showMessage(result.message, true);
                } else {
                    const msg = result.message || (result.errors && result.errors.email ? result.errors.email[0] : 'Something went wrong.');
                    showMessage(msg, false);
                }
            } catch (error) {
                showMessage('Network error. Please try again later.', false);
            } finally {
                getCodeBtn.disabled = false;
                getCodeBtn.value = 'Sign In';
            }
        });

        // Аутентификация по коду
        loginCodeBtn.addEventListener('click', async () => {
            const email = emailInput.value.trim();
            const code = codeInput.value.trim();
            if (!code) {
                showMessage('Please enter the code.', false);
                return;
            }

            // Получаем реферальный код из поля (localStorage уже использован для автоподстановки)
            const referralInput = document.getElementById('referral_code');
            const referral_code = referralInput ? referralInput.value.trim() : "";

            const payload = { email: email, code: code };
            if (referral_code) {
                payload.referral_code = referral_code;
            }

            loginCodeBtn.disabled = true;
            loginCodeBtn.value = 'Verifying code...';

            try {
                const result = await sendRequest('{{ route("login.verify_code") }}', payload);
                if (result.status === 'success') {
                    showMessage(result.message, true);
                    if (result.clear_referral) {
                        localStorage.removeItem('referral_code');
                    }
                    if (result.requires_2fa) {
                        window.location.href = '{{ route("2fa.verify") }}';
                    } else if (result.redirect_url) {
                        window.location.href = result.redirect_url;
                    }
                } else {
                    const msg = result.message || (result.errors && result.errors.code ? result.errors.code[0] : 'Something went wrong.');
                    showMessage(msg, false);
                }
            } catch (error) {
                showMessage('Network error. Please try again later.', false);
            } finally {
                loginCodeBtn.disabled = false;
                loginCodeBtn.value = '';
            }
        });

        // Смена email
        changeEmailLink.addEventListener('click', (e) => {
            e.preventDefault();
            emailBlock.classList.remove('hidden');
            codeBlock.classList.add('hidden');
            actionsBlock.classList.add('hidden');
            emailInput.disabled = false;
            codeInput.value = '';
        });

        // Повторная отправка кода
        resendCodeLink.addEventListener('click', async (e) => {
            e.preventDefault();
            const email = emailInput.value.trim();
            if (!email) {
                showMessage('Email not found.', false);
                return;
            }
            resendCodeLink.style.pointerEvents = 'none';
            resendCodeLink.textContent = 'Отправка...';

            try {
                const result = await sendRequest('{{ route("login.request_code") }}', { email: email });
                if (result.status === 'success') {
                    showMessage(result.message, true);
                } else {
                    const msg = result.message || (result.errors && result.errors.email ? result.errors.email[0] : 'Something went wrong.');
                    showMessage(msg, false);
                }
            } catch (error) {
                showMessage('Network error. Please try again later.', false);
            } finally {
                resendCodeLink.style.pointerEvents = 'auto';
                resendCodeLink.textContent = 'Resend Code';
            }
        });
    </script>
</body>
</html>
