@extends('admin.dashboard.layouts.master')

@section('title', 'Subscription Plans Management')

@section('styles')
<!-- DataTables -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.4.1/css/responsive.bootstrap4.min.css">
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

@endsection

@section('admin-content')
<style>
    .plan-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        color: white;
        display: inline-block;
    }

    .status-active {
        background: #28a745;
    }

    .status-inactive {
        background: #dc3545;
    }

    .plan-features ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .plan-features li {
        font-size: 0.8rem;
        padding: 1px 0;
        color: #6c757d;
    }

    .plan-features li::before {
        content: '•';
        color: #28a745;
        margin-right: 5px;
    }

    .action-buttons {
        display: flex;
        gap: 3px;
        flex-wrap: wrap;
    }

    .action-btn {
        padding: 4px 8px;
        border: none;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        color: white;
    }

    .action-btn.edit {
        background: #007bff;
    }

    .action-btn.edit:hover {
        background: #0056b3;
        color: white;
    }

    .action-btn.toggle {
        background: #ffc107;
        color: #000;
    }

    .action-btn.toggle:hover {
        background: #e0a800;
    }

    .action-btn.delete {
        background: #dc3545;
    }

    .action-btn.delete:hover {
        background: #c82333;
    }
</style>

<!-- Page Title -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Subscription Plans</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="{{ route('admin.index') }}">Dashboard</a></li>
                    <li><span>Subscription Plans</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <div class="user-profile pull-right">
                <img class="avatar user-thumb" src="{{ asset('backend/assets/images/author/avatar.png') }}" alt="avatar">
                <h4 class="user-name dropdown-toggle" data-toggle="dropdown">{{ auth()->user()->name }} <i class="fa fa-angle-down"></i></h4>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="#">Message</a>
                    <a class="dropdown-item" href="#">Settings</a>
                    <a class="dropdown-item" href="#">Log Out</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<div class="main-content-inner">
    <div class="row">
        <!-- Data Table Start -->
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title float-left">Subscription Plans List</h4>
                    <div class="float-right mb-2">
                        <a class="btn btn-primary btn-sm" href="{{ route('admin.subscription-plans.create') }}">
                            <i class="fa fa-plus"></i> Create New Plan
                        </a>
                    </div>
                    <div class="clearfix"></div>

                    @include('admin.dashboard.layouts.partials.messages')

                    <div class="data-tables">
                        <table id="dataTable" class="text-center">
                            <thead class="bg-light text-capitalize">
                                <tr>
                                    <th width="20%">Plan</th>
                                    <th width="10%">Price</th>
                                    <th width="8%">Status</th>
                                    <th width="25%">Features</th>
                                    <th width="10%">Limits</th>
                                    <th width="8%">Active Users</th>
                                    <th width="19%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($plans as $plan)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="plan-badge mr-2" style="background-color: {{ $plan->color }};">
                                                <i class="fa fa-{{ $plan->icon }}"></i>
                                            </div>
                                            <div class="text-left">
                                                <strong>{{ $plan->display_name }}</strong><br>
                                                <small class="text-muted">{{ $plan->name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>${{ number_format($plan->monthly_price, 2) }}</strong>/mo
                                        @if($plan->trial_days > 0)
                                        <br><small class="text-info">{{ $plan->trial_days }}d trial</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="plan-badge {{ $plan->is_active ? 'status-active' : 'status-inactive' }}">
                                            {{ $plan->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="plan-features text-left">
                                            <ul>
                                                @foreach(array_slice($plan->features, 0, 3) as $feature)
                                                <li>{{ $feature }}</li>
                                                @endforeach
                                                @if(count($plan->features) > 3)
                                                <li><em>+{{ count($plan->features) - 3 }} more...</em></li>
                                                @endif
                                            </ul>
                                        </div>
                                    </td>
                                    <td>
                                        <small>
                                            <strong>Cards:</strong> {{ $plan->max_business_cards == -1 ? '∞' : $plan->max_business_cards }}<br>
                                            <strong>Profiles:</strong> {{ $plan->max_club_profiles == -1 ? '∞' : $plan->max_club_profiles }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ $plan->active_subscriptions ?? 0 }}</span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('admin.subscription-plans.edit', $plan->_id) }}" class="action-btn edit">
                                                <i class="fa fa-edit"></i> Edit
                                            </a>
                                            <button onclick="togglePlanStatus('{{ $plan->_id }}', {{ $plan->is_active ? 'false' : 'true' }})"
                                                    class="action-btn toggle">
                                                <i class="fa fa-toggle-{{ $plan->is_active ? 'on' : 'off' }}"></i>
                                                {{ $plan->is_active ? 'Disable' : 'Enable' }}
                                            </button>
                                            @if(($plan->active_subscriptions ?? 0) == 0)
                                            <button onclick="deletePlan('{{ $plan->_id }}', '{{ $plan->display_name }}')"
                                                    class="action-btn delete">
                                                <i class="fa fa-trash"></i> Delete
                                            </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- Data Table End -->
    </div>
</div>

@endsection

@section('scripts')
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.4.1/js/dataTables.responsive.min.js"></script>

<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[0, 'asc']],
            columnDefs: [
                { orderable: false, targets: [6] }
            ]
        });
    });

    function togglePlanStatus(planId, newStatus) {
        const action = newStatus === 'true' ? 'enable' : 'disable';

        Swal.fire({
            title: `${action.charAt(0).toUpperCase() + action.slice(1)} Plan?`,
            text: `Are you sure you want to ${action} this subscription plan?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#007bff',
            cancelButtonColor: '#6c757d',
            confirmButtonText: `Yes, ${action} it!`
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/admin/subscription-plans/${planId}/toggle-status`,
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        Swal.fire({
                            title: 'Success!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonColor: '#007bff'
                        }).then(() => {
                            window.location.reload();
                        });
                    },
                    error: function(xhr) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to update plan status',
                            icon: 'error',
                            confirmButtonColor: '#dc3545'
                        });
                    }
                });
            }
        });
    }

    function deletePlan(planId, planName) {
        Swal.fire({
            title: 'Delete Plan?',
            text: `Are you sure you want to delete "${planName}"? This action cannot be undone.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/admin/subscription-plans/${planId}`,
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        Swal.fire({
                            title: 'Deleted!',
                            text: 'Subscription plan has been deleted.',
                            icon: 'success',
                            confirmButtonColor: '#007bff'
                        }).then(() => {
                            window.location.reload();
                        });
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;
                        Swal.fire({
                            title: 'Error!',
                            text: response.message || 'Failed to delete plan',
                            icon: 'error',
                            confirmButtonColor: '#dc3545'
                        });
                    }
                });
            }
        });
    }


</script>
@endsection
