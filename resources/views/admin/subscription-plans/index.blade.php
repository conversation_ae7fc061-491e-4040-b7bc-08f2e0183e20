<!DOCTYPE html>
<html data-wf-domain="/" data-wf-page="663d095d8e07618c5381377b" data-wf-site="663b748d327c826a2952af46" data-wf-status="1" lang="en" class=" w-mod-js w-mod-ix">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Subscription Plans Management - Admin Panel</title>

  <!-- CSRF Token (Laravel) -->
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <!-- Main CSS -->
  <link rel="stylesheet" href="{{ asset('css/style.css') }}" />
  <link href="/css/techbetatemplates.webflow.14600f26e.css?v=0.3" rel="stylesheet" type="text/css">

  <!-- DataTables -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.4.1/css/responsive.bootstrap5.min.css">
  
  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

  <style>
    .admin-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 40px 20px;
    }

    .admin-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px;
      border-radius: 16px;
      margin-bottom: 30px;
      text-align: center;
    }

    .admin-header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 10px;
    }

    .admin-header p {
      font-size: 1.1rem;
      opacity: 0.9;
    }

    .admin-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      flex-wrap: wrap;
      gap: 15px;
    }

    .btn-group {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .admin-btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .admin-btn.primary {
      background: #28a745;
      color: white;
    }

    .admin-btn.primary:hover {
      background: #218838;
      transform: translateY(-2px);
    }

    .admin-btn.secondary {
      background: #6c757d;
      color: white;
    }

    .admin-btn.secondary:hover {
      background: #5a6268;
    }

    .admin-btn.warning {
      background: #ffc107;
      color: #000;
    }

    .admin-btn.warning:hover {
      background: #e0a800;
    }

    .plans-table-container {
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .table-header {
      background: #f8f9fa;
      padding: 20px;
      border-bottom: 1px solid #dee2e6;
    }

    .table-header h3 {
      margin: 0;
      color: #2c3e50;
      font-size: 1.3rem;
    }

    .plan-badge {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
      color: white;
    }

    .status-active {
      background: #28a745;
    }

    .status-inactive {
      background: #dc3545;
    }

    .plan-features {
      max-width: 200px;
      overflow: hidden;
    }

    .plan-features ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .plan-features li {
      font-size: 0.85rem;
      padding: 2px 0;
      color: #6c757d;
    }

    .plan-features li::before {
      content: '•';
      color: #28a745;
      margin-right: 5px;
    }

    .action-buttons {
      display: flex;
      gap: 5px;
      flex-wrap: wrap;
    }

    .action-btn {
      padding: 6px 12px;
      border: none;
      border-radius: 6px;
      font-size: 0.8rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .action-btn.edit {
      background: #007bff;
      color: white;
    }

    .action-btn.edit:hover {
      background: #0056b3;
    }

    .action-btn.toggle {
      background: #ffc107;
      color: #000;
    }

    .action-btn.toggle:hover {
      background: #e0a800;
    }

    .action-btn.delete {
      background: #dc3545;
      color: white;
    }

    .action-btn.delete:hover {
      background: #c82333;
    }

    .stats-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .stats-number {
      font-size: 2rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 5px;
    }

    .stats-label {
      color: #7f8c8d;
      font-size: 0.9rem;
    }

    @media (max-width: 768px) {
      .admin-actions {
        flex-direction: column;
        align-items: stretch;
      }

      .btn-group {
        justify-content: center;
      }

      .admin-btn {
        flex: 1;
        justify-content: center;
      }
    }
  </style>
</head>

<body>
  <div class="admin-container">
    <div class="admin-header">
      <h1><i class="fas fa-crown"></i> Subscription Plans Management</h1>
      <p>Manage subscription plans, pricing, and features for your platform</p>
    </div>

    <div class="admin-actions">
      <div class="btn-group">
        <a href="{{ route('admin.subscription-plans.create') }}" class="admin-btn primary">
          <i class="fas fa-plus"></i> Create New Plan
        </a>
        <button onclick="seedDefaultPlans()" class="admin-btn secondary">
          <i class="fas fa-seedling"></i> Seed Default Plans
        </button>
      </div>
      
      <div class="btn-group">
        <a href="{{ route('admin.index') }}" class="admin-btn secondary">
          <i class="fas fa-arrow-left"></i> Back to Admin
        </a>
      </div>
    </div>

    <div class="plans-table-container">
      <div class="table-header">
        <h3><i class="fas fa-table"></i> Subscription Plans</h3>
      </div>

      <div class="table-responsive p-3">
        <table id="plansTable" class="table table-striped table-hover">
          <thead>
            <tr>
              <th>Plan</th>
              <th>Price</th>
              <th>Status</th>
              <th>Features</th>
              <th>Limits</th>
              <th>Statistics</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            @foreach($plans as $plan)
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="plan-badge" style="background-color: {{ $plan->color }}; margin-right: 10px;">
                    <i class="fas fa-{{ $plan->icon }}"></i>
                  </div>
                  <div>
                    <strong>{{ $plan->display_name }}</strong><br>
                    <small class="text-muted">{{ $plan->name }}</small>
                  </div>
                </div>
              </td>
              <td>
                <strong>${{ number_format($plan->monthly_price, 2) }}</strong>/month
                @if($plan->trial_days > 0)
                <br><small class="text-info">{{ $plan->trial_days }}-day trial</small>
                @endif
              </td>
              <td>
                <span class="plan-badge {{ $plan->is_active ? 'status-active' : 'status-inactive' }}">
                  {{ $plan->is_active ? 'Active' : 'Inactive' }}
                </span>
              </td>
              <td>
                <div class="plan-features">
                  <ul>
                    @foreach(array_slice($plan->features, 0, 3) as $feature)
                    <li>{{ $feature }}</li>
                    @endforeach
                    @if(count($plan->features) > 3)
                    <li><em>+{{ count($plan->features) - 3 }} more...</em></li>
                    @endif
                  </ul>
                </div>
              </td>
              <td>
                <small>
                  <strong>Cards:</strong> {{ $plan->max_business_cards == -1 ? '∞' : $plan->max_business_cards }}<br>
                  <strong>Profiles:</strong> {{ $plan->max_club_profiles == -1 ? '∞' : $plan->max_club_profiles }}
                </small>
              </td>
              <td>
                <div class="stats-card">
                  <div class="stats-number">{{ $plan->active_subscriptions ?? 0 }}</div>
                  <div class="stats-label">Active</div>
                </div>
              </td>
              <td>
                <div class="action-buttons">
                  <a href="{{ route('admin.subscription-plans.edit', $plan->_id) }}" class="action-btn edit">
                    <i class="fas fa-edit"></i> Edit
                  </a>
                  <button onclick="togglePlanStatus('{{ $plan->_id }}', {{ $plan->is_active ? 'false' : 'true' }})" 
                          class="action-btn toggle">
                    <i class="fas fa-toggle-{{ $plan->is_active ? 'on' : 'off' }}"></i>
                    {{ $plan->is_active ? 'Disable' : 'Enable' }}
                  </button>
                  @if(($plan->active_subscriptions ?? 0) == 0)
                  <button onclick="deletePlan('{{ $plan->_id }}', '{{ $plan->display_name }}')" 
                          class="action-btn delete">
                    <i class="fas fa-trash"></i> Delete
                  </button>
                  @endif
                </div>
              </td>
            </tr>
            @endforeach
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- DataTables JS -->
  <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
  <script src="https://cdn.datatables.net/responsive/2.4.1/js/dataTables.responsive.min.js"></script>

  <script>
    $(document).ready(function() {
      $('#plansTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[0, 'asc']],
        columnDefs: [
          { orderable: false, targets: [6] }
        ]
      });
    });

    function togglePlanStatus(planId, newStatus) {
      const action = newStatus === 'true' ? 'enable' : 'disable';
      
      Swal.fire({
        title: `${action.charAt(0).toUpperCase() + action.slice(1)} Plan?`,
        text: `Are you sure you want to ${action} this subscription plan?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#667eea',
        cancelButtonColor: '#95a5a6',
        confirmButtonText: `Yes, ${action} it!`
      }).then((result) => {
        if (result.isConfirmed) {
          $.ajax({
            url: `/admin/subscription-plans/${planId}/toggle-status`,
            method: 'POST',
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
              Swal.fire({
                title: 'Success!',
                text: response.message,
                icon: 'success',
                confirmButtonColor: '#667eea'
              }).then(() => {
                window.location.reload();
              });
            },
            error: function(xhr) {
              Swal.fire({
                title: 'Error!',
                text: 'Failed to update plan status',
                icon: 'error',
                confirmButtonColor: '#e74c3c'
              });
            }
          });
        }
      });
    }

    function deletePlan(planId, planName) {
      Swal.fire({
        title: 'Delete Plan?',
        text: `Are you sure you want to delete "${planName}"? This action cannot be undone.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#e74c3c',
        cancelButtonColor: '#95a5a6',
        confirmButtonText: 'Yes, delete it!'
      }).then((result) => {
        if (result.isConfirmed) {
          $.ajax({
            url: `/admin/subscription-plans/${planId}`,
            method: 'DELETE',
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
              Swal.fire({
                title: 'Deleted!',
                text: 'Subscription plan has been deleted.',
                icon: 'success',
                confirmButtonColor: '#667eea'
              }).then(() => {
                window.location.reload();
              });
            },
            error: function(xhr) {
              const response = xhr.responseJSON;
              Swal.fire({
                title: 'Error!',
                text: response.message || 'Failed to delete plan',
                icon: 'error',
                confirmButtonColor: '#e74c3c'
              });
            }
          });
        }
      });
    }

    function seedDefaultPlans() {
      Swal.fire({
        title: 'Seed Default Plans?',
        text: 'This will create or update the default subscription plans.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#667eea',
        cancelButtonColor: '#95a5a6',
        confirmButtonText: 'Yes, seed them!'
      }).then((result) => {
        if (result.isConfirmed) {
          $.ajax({
            url: '/admin/subscription-plans/seed-defaults',
            method: 'POST',
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
              Swal.fire({
                title: 'Success!',
                text: 'Default plans have been seeded successfully!',
                icon: 'success',
                confirmButtonColor: '#667eea'
              }).then(() => {
                window.location.reload();
              });
            },
            error: function(xhr) {
              Swal.fire({
                title: 'Error!',
                text: 'Failed to seed default plans',
                icon: 'error',
                confirmButtonColor: '#e74c3c'
              });
            }
          });
        }
      });
    }
  </script>
</body>
</html>
