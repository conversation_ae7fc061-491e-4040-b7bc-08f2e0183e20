<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Subscription System</title>
    <link href="/css/techbetatemplates.webflow.14600f26e.css" rel="stylesheet" type="text/css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        .test-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .info-value {
            font-size: 18px;
            color: #333;
        }
        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
        .plan-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .plan-card:hover {
            border-color: #007bff;
            transform: translateY(-5px);
        }
        .plan-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .plan-price {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 15px;
        }
        .plan-features {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        .plan-features li {
            padding: 5px 0;
            color: #666;
        }
        .btn-test {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease;
        }
        .btn-test:hover {
            background: #0056b3;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section">
            <h1 class="test-title">🧪 Subscription System Test</h1>
            
            <div class="test-info">
                <div class="info-card">
                    <div class="info-label">Test User</div>
                    <div class="info-value"><EMAIL></div>
                </div>
                <div class="info-card">
                    <div class="info-label">Balance</div>
                    <div class="info-value">$1000.00</div>
                </div>
                <div class="info-card">
                    <div class="info-label">Subscription Status</div>
                    <div class="info-value">
                        <span class="status-badge status-active">Active</span>
                    </div>
                </div>
                <div class="info-card">
                    <div class="info-label">Current Plan</div>
                    <div class="info-value">Basic Plan</div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="/subscriptions/plans" class="btn-test">View Subscription Plans</a>
                <a href="/business-cards/create" class="btn-test" style="margin-left: 10px;">Create Business Card</a>
                <a href="/club/manage" class="btn-test" style="margin-left: 10px;">Manage Club Profile</a>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">📋 Available Subscription Plans</h2>
            
            <div class="plans-grid">
                <div class="plan-card">
                    <div class="plan-name">Free</div>
                    <div class="plan-price">$0<span style="font-size: 16px;">/month</span></div>
                    <ul class="plan-features">
                        <li>0 Business Cards</li>
                        <li>0 Club Profiles</li>
                        <li>Basic Support</li>
                    </ul>
                </div>
                
                <div class="plan-card">
                    <div class="plan-name">Basic</div>
                    <div class="plan-price">$9.99<span style="font-size: 16px;">/month</span></div>
                    <ul class="plan-features">
                        <li>1 Business Card</li>
                        <li>1 Club Profile</li>
                        <li>Email Support</li>
                    </ul>
                </div>
                
                <div class="plan-card">
                    <div class="plan-name">Standard</div>
                    <div class="plan-price">$19.99<span style="font-size: 16px;">/month</span></div>
                    <ul class="plan-features">
                        <li>3 Business Cards</li>
                        <li>2 Club Profiles</li>
                        <li>Priority Support</li>
                    </ul>
                </div>
                
                <div class="plan-card">
                    <div class="plan-name">Premium</div>
                    <div class="plan-price">$39.99<span style="font-size: 16px;">/month</span></div>
                    <ul class="plan-features">
                        <li>10 Business Cards</li>
                        <li>5 Club Profiles</li>
                        <li>24/7 Support</li>
                    </ul>
                </div>
                
                <div class="plan-card">
                    <div class="plan-name">VIP</div>
                    <div class="plan-price">$99.99<span style="font-size: 16px;">/month</span></div>
                    <ul class="plan-features">
                        <li>Unlimited Business Cards</li>
                        <li>Unlimited Club Profiles</li>
                        <li>VIP Support</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">🔧 Test Actions</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <button class="btn-test" onclick="testSubscriptionAccess()">Test Subscription Access</button>
                <button class="btn-test" onclick="testBusinessCardCreation()">Test Business Card Creation</button>
                <button class="btn-test" onclick="testClubProfileCreation()">Test Club Profile Creation</button>
                <button class="btn-test" onclick="testPlanUpgrade()">Test Plan Upgrade</button>
            </div>
            
            <div id="test-results" style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; display: none;">
                <h3>Test Results:</h3>
                <div id="test-output"></div>
            </div>
        </div>
    </div>

    <script>
        function showTestResult(message, success = true) {
            const resultsDiv = document.getElementById('test-results');
            const outputDiv = document.getElementById('test-output');
            
            outputDiv.innerHTML = `<div style="color: ${success ? 'green' : 'red'}; font-weight: bold;">${message}</div>`;
            resultsDiv.style.display = 'block';
        }

        function testSubscriptionAccess() {
            showTestResult('✅ Subscription access test: User has active Basic subscription');
        }

        function testBusinessCardCreation() {
            window.location.href = '/business-cards/create';
        }

        function testClubProfileCreation() {
            window.location.href = '/club/manage';
        }

        function testPlanUpgrade() {
            window.location.href = '/subscriptions/plans';
        }
    </script>
</body>
</html>
