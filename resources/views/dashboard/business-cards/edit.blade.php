<!DOCTYPE html>
<html data-wf-page="6684b8b8b8b8b8b8b8b8b8b8" data-wf-site="6684b8b8b8b8b8b8b8b8b8b8" lang="en">
<head>
    <meta charset="utf-8">
    <title>Edit Business Card - EVOLUTION888 business center</title>
    <meta content="Edit your business card" name="description">
    <meta content="Edit Business Card - EVOLUTION888 business center" property="og:title">
    <meta content="Edit your business card" property="og:description">
    <meta content="Edit Business Card - EVOLUTION888 business center" property="twitter:title">
    <meta content="Edit your business card" property="twitter:description">
    <meta property="og:type" content="website">
    <meta content="summary_large_image" name="twitter:card">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <meta content="Webflow" name="generator">
    <link href="/css/techbetatemplates.webflow.14600f26e.css?v=0.3" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com" rel="preconnect">
    <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js" type="text/javascript"></script>
    <script type="text/javascript">WebFont.load({  google: {    families: ["Inter:100,200,300,regular,500,600,700,800,900"]  }});</script>
    <script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script>
    <link href="/images/favicon.ico" rel="shortcut icon" type="image/x-icon">
    <link href="/images/webclip.png" rel="apple-touch-icon">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
        .edit-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 80px 0 40px;
            margin-top: -120px;
            color: white;
        }

        .form-modern {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 50px;
            margin-top: -40px;
            position: relative;
            z-index: 10;
        }

        .form-section {
            margin-bottom: 40px;
        }

        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
            font-size: 20px;
            font-weight: 600;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 25px;
        }

        .form-group {
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-label.required::after {
            content: ' *';
            color: #e74c3c;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .file-upload {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }

        .file-upload input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .file-upload-label {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            border: 2px dashed #667eea;
            border-radius: 10px;
            background: #f8f9fa;
            color: #667eea;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .file-upload-label:hover {
            background: #667eea;
            color: white;
        }

        .file-upload-label i {
            margin-right: 10px;
            font-size: 18px;
        }

        .current-image {
            margin-top: 15px;
            text-align: center;
        }

        .current-image img {
            max-width: 150px;
            max-height: 150px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .social-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .social-input-group {
            position: relative;
        }

        .social-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            font-size: 18px;
            z-index: 2;
        }

        .social-input {
            padding-left: 50px !important;
        }

        .btn-modern {
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-secondary-modern {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }

        .btn-secondary-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(149, 165, 166, 0.4);
            color: white;
        }

        .form-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
        }

        .preview-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 30px;
            color: white;
            text-align: center;
            margin-bottom: 30px;
        }

        .preview-card h4 {
            margin-bottom: 15px;
            font-size: 18px;
        }

        .preview-card p {
            margin-bottom: 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    @include('partials.navbar')

    <!-- Hero Section -->
    <section class="edit-hero">
        <div class="w-layout-blockcontainer container-default w-container">
            <div class="text-center">
                <h1 class="display-4 mb-3">Edit Business Card</h1>
                <p class="lead">Update your professional business card information</p>
                <a href="{{ route('dashboard.business-cards.index') }}" class="btn-secondary-modern btn-modern">
                    <i class="fas fa-arrow-left"></i> Back to Cards
                </a>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="section">
        <div class="w-layout-blockcontainer container-default w-container">

            <div class="preview-card">
                <h4><i class="fas fa-eye"></i> Live Preview</h4>
                <p>Your changes will be reflected in real-time on your public business card</p>
            </div>

            <div class="form-modern">
                <form id="editBusinessCardForm" method="POST" action="{{ route('dashboard.business-cards.update', $businessCard->_id) }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3><i class="fas fa-id-card"></i> Basic Information</h3>

                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label required">Card Title</label>
                                <input type="text" name="title" class="form-input" value="{{ old('title', $businessCard->title) }}" required placeholder="Enter card title">
                            </div>
                            <div class="form-group">
                                <label class="form-label required">Profile Name (URL)</label>
                                <input type="text" name="profile_name" class="form-input" value="{{ old('profile_name', $businessCard->profile_name) }}" required placeholder="your-profile-name">
                                <small style="color: #6c757d; margin-top: 5px; display: block;">
                                    <i class="fas fa-link"></i> Your card URL: /tag/{{ $businessCard->profile_name }}
                                </small>
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">Company</label>
                                <input type="text" name="company" class="form-input" value="{{ old('company', $businessCard->company) }}" placeholder="Company name">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Position</label>
                                <input type="text" name="position" class="form-input" value="{{ old('position', $businessCard->position) }}" placeholder="Job title">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea name="description" class="form-input form-textarea" placeholder="Tell people about yourself and your business...">{{ old('description', $businessCard->description) }}</textarea>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="form-section">
                        <h3><i class="fas fa-address-book"></i> Contact Information</h3>

                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">Email</label>
                                <input type="email" name="email" class="form-input" value="{{ old('email', $businessCard->contacts['email'] ?? '') }}" placeholder="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Phone</label>
                                <input type="text" name="phone" class="form-input" value="{{ old('phone', $businessCard->contacts['phone'] ?? '') }}" placeholder="+****************">
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">Website</label>
                                <input type="url" name="website" class="form-input" value="{{ old('website', $businessCard->contacts['website'] ?? '') }}" placeholder="https://yourwebsite.com">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Location</label>
                                <input type="text" name="location" class="form-input" value="{{ old('location', $businessCard->contacts['location'] ?? '') }}" placeholder="City, Country">
                            </div>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div class="form-section">
                        <h3><i class="fab fa-share-alt"></i> Social Media</h3>

                        <div class="social-grid">
                            <div class="social-input-group">
                                <i class="fab fa-linkedin social-icon"></i>
                                <input type="url" name="linkedin" class="form-input social-input" value="{{ old('linkedin', $businessCard->contacts['linkedin'] ?? '') }}" placeholder="LinkedIn profile URL">
                            </div>
                            <div class="social-input-group">
                                <i class="fab fa-twitter social-icon"></i>
                                <input type="url" name="twitter" class="form-input social-input" value="{{ old('twitter', $businessCard->contacts['twitter'] ?? '') }}" placeholder="Twitter profile URL">
                            </div>
                            <div class="social-input-group">
                                <i class="fab fa-facebook social-icon"></i>
                                <input type="url" name="facebook" class="form-input social-input" value="{{ old('facebook', $businessCard->contacts['facebook'] ?? '') }}" placeholder="Facebook profile URL">
                            </div>
                            <div class="social-input-group">
                                <i class="fab fa-instagram social-icon"></i>
                                <input type="url" name="instagram" class="form-input social-input" value="{{ old('instagram', $businessCard->contacts['instagram'] ?? '') }}" placeholder="Instagram profile URL">
                            </div>
                            <div class="social-input-group">
                                <i class="fab fa-telegram social-icon"></i>
                                <input type="text" name="telegram" class="form-input social-input" value="{{ old('telegram', $businessCard->contacts['telegram'] ?? '') }}" placeholder="@username or t.me/username">
                            </div>
                            <div class="social-input-group">
                                <i class="fab fa-whatsapp social-icon"></i>
                                <input type="text" name="whatsapp" class="form-input social-input" value="{{ old('whatsapp', $businessCard->contacts['whatsapp'] ?? '') }}" placeholder="WhatsApp number">
                            </div>
                        </div>
                    </div>

                    <!-- Media & Tags -->
                    <div class="form-section">
                        <h3><i class="fas fa-image"></i> Media & Tags</h3>

                        <div class="form-group">
                            <label class="form-label">Logo</label>
                            <div class="file-upload">
                                <input type="file" name="logo" id="logo" accept="image/*">
                                <label for="logo" class="file-upload-label">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    Choose Logo Image
                                </label>
                            </div>
                            @if($businessCard->logo)
                                <div class="current-image">
                                    <img src="{{ $businessCard->logo }}" alt="Current Logo">
                                    <p style="margin-top: 10px; color: #6c757d; font-size: 14px;">Current logo (upload new to replace)</p>
                                </div>
                            @endif
                        </div>

                        <div class="form-group">
                            <label class="form-label">Tags</label>
                            <input type="text" name="tags" class="form-input" value="{{ old('tags', is_array($businessCard->tags) ? implode(', ', $businessCard->tags) : $businessCard->tags) }}" placeholder="business, technology, innovation, consulting">
                            <small style="color: #6c757d; margin-top: 5px; display: block;">
                                <i class="fas fa-tag"></i> Separate tags with commas to help people find your card
                            </small>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="submit" class="btn-primary-modern btn-modern">
                            <i class="fas fa-save"></i> Update Business Card
                        </button>
                        <a href="{{ route('dashboard.business-cards.index') }}" class="btn-secondary-modern btn-modern">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>

        </div>
    </section>
    
    @include('partials.footer')

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            // File upload preview
            $('#logo').on('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('.current-image').remove();
                        $('.file-upload').after(`
                            <div class="current-image">
                                <img src="${e.target.result}" alt="Logo Preview">
                                <p style="margin-top: 10px; color: #6c757d; font-size: 14px;">New logo preview</p>
                            </div>
                        `);
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Form submission
            $('#editBusinessCardForm').on('submit', function(e) {
                e.preventDefault();

                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();

                // Show loading state
                submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Updating...');
                submitBtn.prop('disabled', true);

                const formData = new FormData(this);

                // Debug: Log form data
                console.log('Form data being sent:');
                for (let [key, value] of formData.entries()) {
                    console.log(key, value);
                }

                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        Swal.fire({
                            title: 'Success!',
                            text: 'Your business card has been updated successfully!',
                            icon: 'success',
                            confirmButtonColor: '#667eea',
                            confirmButtonText: 'Great!'
                        }).then(() => {
                            window.location.href = '{{ route("dashboard.business-cards.index") }}';
                        });
                    },
                    error: function(xhr) {
                        let errorMessage = 'An error occurred while updating your business card.';

                        if (xhr.responseJSON && xhr.responseJSON.errors) {
                            const errors = xhr.responseJSON.errors;
                            errorMessage = 'Please fix the following errors:<br><br>';
                            Object.keys(errors).forEach(key => {
                                errorMessage += `• ${errors[key][0]}<br>`;
                            });
                        }

                        Swal.fire({
                            title: 'Error!',
                            html: errorMessage,
                            icon: 'error',
                            confirmButtonColor: '#dc3545'
                        });
                    },
                    complete: function() {
                        // Restore button state
                        submitBtn.html(originalText);
                        submitBtn.prop('disabled', false);
                    }
                });
            });
        });
    </script>
</body>
</html>
