<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SubscriptionRequired
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string|null  $requiredPlan
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $requiredPlan = null)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Check if user has any active subscription
        if (!$user->hasActiveSubscription()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Active subscription required to access this feature.',
                    'redirect_url' => route('subscriptions.plans'),
                    'show_upgrade_modal' => true
                ], 403);
            }
            
            return redirect()->route('subscriptions.plans')
                           ->with('error', 'You need an active subscription to access this feature.')
                           ->with('show_upgrade_modal', true);
        }

        // Check specific plan requirement if specified
        if ($requiredPlan) {
            $activeSubscription = $user->activeSubscription();
            $userPlan = $activeSubscription->plan->name;
            
            // Define plan hierarchy for comparison
            $planHierarchy = [
                'free' => 0,
                'basic' => 1,
                'standard' => 2,
                'premium' => 3,
                'vip' => 4
            ];
            
            $requiredLevel = $planHierarchy[$requiredPlan] ?? 0;
            $userLevel = $planHierarchy[$userPlan] ?? 0;
            
            if ($userLevel < $requiredLevel) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'status' => 'error',
                        'message' => "This feature requires {$requiredPlan} plan or higher. Your current plan: {$userPlan}",
                        'redirect_url' => route('subscriptions.plans'),
                        'show_upgrade_modal' => true,
                        'current_plan' => $userPlan,
                        'required_plan' => $requiredPlan
                    ], 403);
                }
                
                return redirect()->route('subscriptions.plans')
                               ->with('error', "This feature requires {$requiredPlan} plan or higher. Your current plan: {$userPlan}")
                               ->with('show_upgrade_modal', true)
                               ->with('required_plan', $requiredPlan);
            }
        }

        return $next($request);
    }
}
