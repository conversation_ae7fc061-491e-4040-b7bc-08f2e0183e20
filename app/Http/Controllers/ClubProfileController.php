<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\ClubProfile;
use App\Models\User;

class ClubProfileController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except(['show', 'index']);
    }

    /**
     * Display a listing of club members (public view)
     */
    public function index(Request $request)
    {
        $query = ClubProfile::active()->approved()->with('user');
        
        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $query->search($request->search);
        }
        
        // Filter by tags
        if ($request->has('tags') && !empty($request->tags)) {
            $tags = is_array($request->tags) ? $request->tags : explode(',', $request->tags);
            $query->withTags($tags);
        }
        
        $profiles = $query->orderBy('created_at', 'desc')->paginate(12);
        
        // Get all unique tags for filter
        $allTags = ClubProfile::active()->approved()
                             ->pluck('tags')
                             ->flatten()
                             ->unique()
                             ->sort()
                             ->values();
        
        return view('club.members', compact('profiles', 'allTags'));
    }

    /**
     * Display the specified club profile (public view)
     */
    public function show($profileName)
    {
        $profile = ClubProfile::byProfileName($profileName)
                             ->active()
                             ->approved()
                             ->with('user')
                             ->first();

        if (!$profile) {
            abort(404, 'Club profile not found');
        }

        // Increment views count
        $profile->incrementViews();

        // Check if current user has subscription to view contacts
        $canViewContacts = false;
        if (Auth::check()) {
            $user = Auth::user();
            $subscription = $user->activeSubscription();
            // Allow contact viewing for users with any paid subscription (not free plan)
            $canViewContacts = $subscription && $subscription->plan && $subscription->plan->monthly_price > 0;
        }

        return view('club.profile', compact('profile', 'canViewContacts'));
    }

    /**
     * Show user's own club profile management
     */
    public function manage()
    {
        $user = Auth::user();

        // Check subscription quota
        if (!$user->canCreateClubProfiles()) {
            return redirect()->route('subscriptions.plans')
                           ->with('error', 'You need an active subscription to manage club profiles.')
                           ->with('show_upgrade_modal', true);
        }

        $profile = $user->clubProfile;

        return view('club.manage', compact('profile'));
    }

    /**
     * Display user's club profile in dashboard
     */
    public function userIndex()
    {
        $user = Auth::user();
        $profile = $user->clubProfile;

        return view('dashboard.club-profile.index', compact('profile'));
    }

    /**
     * Show the form for creating a new club profile
     */
    public function userCreate()
    {
        $user = Auth::user();

        // Check if user already has a club profile
        if ($user->clubProfile) {
            return redirect()->route('dashboard.club-profile.index')
                ->with('error', 'You already have a club profile.');
        }

        // Check if user has permission to create club profile
        if (!$user->can('create_club_profile')) {
            return redirect()->route('dashboard.club-profile.index')
                ->with('error', 'You need an active subscription to create a club profile.');
        }

        return view('dashboard.club-profile.create');
    }

    /**
     * Store a newly created club profile
     */
    public function userStore(Request $request)
    {
        $user = Auth::user();

        // Check if user already has a club profile
        if ($user->clubProfile) {
            return response()->json(['error' => 'You already have a club profile.'], 422);
        }

        // Check if user has permission to create club profile
        if (!$user->can('create_club_profile')) {
            return response()->json(['error' => 'You need an active subscription to create a club profile.'], 403);
        }

        $validated = $request->validate([
            'profile_name' => 'required|string|max:255|unique:club_profiles,profile_name',
            'display_name' => 'required|string|max:255',
            'bio' => 'nullable|string|max:1000',
            'company' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'website' => 'nullable|url|max:255',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'social_links' => 'nullable|string',
            'tags' => 'nullable|string',
            'is_public' => 'boolean'
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $validated['avatar'] = '/storage/' . $avatarPath;
        }

        // Process social links
        if (!empty($validated['social_links'])) {
            $socialLinks = array_filter(array_map('trim', explode("\n", $validated['social_links'])));
            $validated['social_links'] = $socialLinks;
        } else {
            $validated['social_links'] = [];
        }

        // Process tags
        if (!empty($validated['tags'])) {
            $tags = array_filter(array_map('trim', explode(',', $validated['tags'])));
            $validated['tags'] = $tags;
        } else {
            $validated['tags'] = [];
        }

        $validated['user_id'] = $user->_id;
        $validated['is_public'] = $request->has('is_public');
        $validated['moderation_status'] = 'pending';

        ClubProfile::create($validated);

        if ($request->expectsJson()) {
            return response()->json(['success' => 'Club profile created successfully!']);
        }

        return redirect()->route('dashboard.club-profile.index')
            ->with('success', 'Club profile created successfully!');
    }

    /**
     * Show the form for editing the user's club profile
     */
    public function userEdit(ClubProfile $clubProfile)
    {
        $user = Auth::user();

        // Check if user owns this profile
        if ($clubProfile->user_id !== $user->_id) {
            return redirect()->route('dashboard.club-profile.index')
                ->with('error', 'You can only edit your own club profile.');
        }

        return view('dashboard.club-profile.edit', compact('clubProfile'));
    }

    /**
     * Update the user's club profile
     */
    public function userUpdate(Request $request, ClubProfile $clubProfile)
    {
        $user = Auth::user();

        // Check if user owns this profile
        if ($clubProfile->user_id !== $user->_id) {
            return response()->json(['error' => 'You can only edit your own club profile.'], 403);
        }

        $validated = $request->validate([
            'profile_name' => 'required|string|max:255|unique:club_profiles,profile_name,' . $clubProfile->_id,
            'display_name' => 'required|string|max:255',
            'bio' => 'nullable|string|max:1000',
            'company' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'website' => 'nullable|url|max:255',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'social_links' => 'nullable|string',
            'tags' => 'nullable|string',
            'is_public' => 'boolean'
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($clubProfile->avatar && file_exists(public_path($clubProfile->avatar))) {
                unlink(public_path($clubProfile->avatar));
            }

            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $validated['avatar'] = '/storage/' . $avatarPath;
        }

        // Process social links
        if (!empty($validated['social_links'])) {
            $socialLinks = array_filter(array_map('trim', explode("\n", $validated['social_links'])));
            $validated['social_links'] = $socialLinks;
        } else {
            $validated['social_links'] = [];
        }

        // Process tags
        if (!empty($validated['tags'])) {
            $tags = array_filter(array_map('trim', explode(',', $validated['tags'])));
            $validated['tags'] = $tags;
        } else {
            $validated['tags'] = [];
        }

        $validated['is_public'] = $request->has('is_public');
        $validated['moderation_status'] = 'pending'; // Reset to pending after edit

        $clubProfile->update($validated);

        if ($request->expectsJson()) {
            return response()->json(['success' => 'Club profile updated successfully!']);
        }

        return redirect()->route('dashboard.club-profile.index')
            ->with('success', 'Club profile updated successfully!');
    }

    /**
     * Remove the user's club profile
     */
    public function userDestroy(ClubProfile $clubProfile)
    {
        $user = Auth::user();

        // Check if user owns this profile
        if ($clubProfile->user_id !== $user->_id) {
            return response()->json(['error' => 'You can only delete your own club profile.'], 403);
        }

        // Delete avatar if exists
        if ($clubProfile->avatar && file_exists(public_path($clubProfile->avatar))) {
            unlink(public_path($clubProfile->avatar));
        }

        $clubProfile->delete();

        return response()->json(['success' => 'Club profile deleted successfully!']);
    }

    /**
     * Store or update user's club profile
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        // Check if user can access club members
        if (!$user->canAccessClubMembers()) {
            return response()->json([
                'status' => 'error',
                'message' => 'You need an active membership to create a club profile.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'profile_name' => 'nullable|string|max:100|regex:/^[a-zA-Z0-9\-_]+$/',
            'banner_image' => 'nullable|string', // Base64 image
            'description' => 'required|string|max:2000',
            'tags' => 'nullable|array|max:10',
            'tags.*' => 'string|max:50'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $profile = $user->clubProfile;
            
            if ($profile) {
                // Update existing profile
                $profile->update([
                    'profile_name' => $request->profile_name ?? $profile->profile_name,
                    'banner_image' => $request->banner_image ?? $profile->banner_image,
                    'description' => $request->description,
                    'tags' => $request->tags ?? [],
                    'is_active' => true
                ]);
                $message = 'Club profile updated successfully! It will be reviewed by moderators.';
            } else {
                // Create new profile
                $profile = ClubProfile::create([
                    'user_id' => $user->id,
                    'profile_name' => $request->profile_name,
                    'banner_image' => $request->banner_image,
                    'description' => $request->description,
                    'tags' => $request->tags ?? [],
                    'is_active' => true
                ]);
                $message = 'Club profile created successfully! It will be reviewed by moderators.';
            }

            return response()->json([
                'status' => 'success',
                'message' => $message,
                'redirect_url' => route('club.manage')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error saving club profile: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle club profile active status
     */
    public function toggleActive()
    {
        $user = Auth::user();
        $profile = $user->clubProfile;
        
        if (!$profile) {
            return response()->json([
                'status' => 'error',
                'message' => 'No club profile found'
            ], 404);
        }

        $profile->update(['is_active' => !$profile->is_active]);
        
        return response()->json([
            'status' => 'success',
            'message' => 'Profile status updated',
            'is_active' => $profile->is_active
        ]);
    }

    /**
     * Delete user's club profile
     */
    public function destroy()
    {
        $user = Auth::user();
        $profile = $user->clubProfile;
        
        if (!$profile) {
            return response()->json([
                'status' => 'error',
                'message' => 'No club profile found'
            ], 404);
        }

        try {
            $profile->delete();
            
            return response()->json([
                'status' => 'success',
                'message' => 'Club profile deleted successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error deleting club profile: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Admin: Get profiles pending moderation
     */
    public function pendingModeration()
    {
        $this->middleware('role:admin|moderator');
        
        $profiles = ClubProfile::pending()
                              ->with('user')
                              ->orderBy('created_at', 'asc')
                              ->paginate(20);
        
        return view('admin.club-profiles.pending', compact('profiles'));
    }

    /**
     * Admin: Approve club profile
     */
    public function approve(Request $request, $id)
    {
        $this->middleware('role:admin|moderator');
        
        $profile = ClubProfile::findOrFail($id);
        $profile->approve(Auth::id(), $request->notes);
        
        return response()->json([
            'status' => 'success',
            'message' => 'Profile approved successfully!'
        ]);
    }

    /**
     * Admin: Reject club profile
     */
    public function reject(Request $request, $id)
    {
        $this->middleware('role:admin|moderator');
        
        $validator = Validator::make($request->all(), [
            'notes' => 'required|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $profile = ClubProfile::findOrFail($id);
        $profile->reject(Auth::id(), $request->notes);
        
        return response()->json([
            'status' => 'success',
            'message' => 'Profile rejected successfully!'
        ]);
    }

    /**
     * Get profile data for editing (AJAX)
     */
    public function getProfileData()
    {
        $user = Auth::user();
        $profile = $user->clubProfile;
        
        if (!$profile) {
            return response()->json([
                'status' => 'error',
                'message' => 'No profile found'
            ], 404);
        }
        
        return response()->json([
            'status' => 'success',
            'profile' => [
                'profile_name' => $profile->profile_name,
                'banner_image' => $profile->banner_image,
                'description' => $profile->description,
                'tags' => $profile->tags,
                'is_active' => $profile->is_active,
                'moderation_status' => $profile->moderation_status,
                'moderation_notes' => $profile->moderation_notes
            ]
        ]);
    }

    /**
     * Check profile name availability (AJAX)
     */
    public function checkProfileName(Request $request)
    {
        $profileName = $request->profile_name;
        $user = Auth::user();
        
        if (empty($profileName)) {
            return response()->json(['available' => false]);
        }
        
        $exists = ClubProfile::where('profile_name', $profileName)
                            ->where('user_id', '!=', $user->id)
                            ->exists();
        
        return response()->json(['available' => !$exists]);
    }
}
