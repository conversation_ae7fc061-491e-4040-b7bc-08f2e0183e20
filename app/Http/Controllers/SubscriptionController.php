<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use App\Models\SubscriptionTransaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display subscription plans
     */
    public function plans()
    {
        $user = Auth::user();
        $plans = SubscriptionPlan::active()->ordered()->get();
        $currentSubscription = $user->activeSubscription();

        return view('subscriptions.plans', compact('plans', 'currentSubscription'));
    }

    /**
     * Show subscription purchase form
     */
    public function purchase($planId)
    {
        $user = Auth::user();
        $plan = SubscriptionPlan::active()->findOrFail($planId);
        $currentSubscription = $user->activeSubscription();
        
        // Check if user already has this plan
        if ($currentSubscription && $currentSubscription->plan_id == $planId) {
            return redirect()->route('subscriptions.plans')
                           ->with('info', 'You already have this subscription plan.');
        }

        return view('subscriptions.purchase', compact('plan', 'currentSubscription'));
    }

    /**
     * Process subscription purchase
     */
    public function processPurchase(Request $request, $planId)
    {
        $request->validate([
            'payment_method' => 'required|string',
            'auto_renew' => 'nullable|in:true,false,1,0'
        ]);

        $user = Auth::user();
        $plan = SubscriptionPlan::active()->findOrFail($planId);

        try {
            // Cancel existing subscription if any
            $existingSubscription = $user->activeSubscription();
            if ($existingSubscription) {
                $existingSubscription->cancel('Upgraded to new plan');
            }

            // Check if user has enough balance (for now, using balance-based payment)
            if ($user->balance < $plan->monthly_price) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Insufficient balance. Please top up your account.'
                ], 400);
            }

            // Create new subscription
            $subscription = UserSubscription::create([
                'user_id' => $user->_id,
                'plan_id' => $plan->_id,
                'status' => UserSubscription::STATUS_ACTIVE,
                'starts_at' => now(),
                'expires_at' => now()->addMonth(),
                'next_billing_date' => now()->addMonth(),
                'auto_renew' => $request->boolean('auto_renew', true),
                'payment_method' => $request->payment_method,
                'amount_paid' => $plan->monthly_price
            ]);

            // Create transaction record
            $transaction = SubscriptionTransaction::createSubscriptionTransaction(
                $user->_id,
                $subscription->_id,
                $plan->_id,
                $plan->monthly_price
            );

            // Deduct from user balance
            $user->decrement('balance', $plan->monthly_price);

            // Create transaction record in old system for compatibility
            \App\Models\Transaction::create([
                'user_id' => $user->_id,
                'level' => 1,
                'amount' => -$plan->monthly_price,
                'category' => 'subscription',
                'type' => 'subscription_purchase',
                'status' => 'completed',
                'description' => "Subscription purchase: {$plan->display_name}"
            ]);

            // Mark transaction as completed
            $transaction->markCompleted([
                'payment_method' => $request->payment_method,
                'balance_deducted' => $plan->monthly_price
            ]);

            // Assign subscriber role
            $user->assignSubscriberRoleFromPlan($plan->name);

            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Subscription purchased successfully!',
                    'subscription' => $subscription->load('plan'),
                    'redirect_url' => route('dashboard')
                ]);
            }

            return redirect()->route('dashboard')
                           ->with('success', 'Subscription purchased successfully!');

        } catch (\Exception $e) {
            
            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to process subscription purchase: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                           ->with('error', 'Failed to process subscription purchase. Please try again.');
        }
    }



    /**
     * Cancel subscription
     */
    public function cancel(Request $request)
    {
        $user = Auth::user();
        $subscription = $user->activeSubscription();

        if (!$subscription) {
            return response()->json([
                'status' => 'error',
                'message' => 'No active subscription found.'
            ], 404);
        }

        $subscription->cancel($request->input('reason', 'User requested cancellation'));

        return response()->json([
            'status' => 'success',
            'message' => 'Subscription cancelled successfully.'
        ]);
    }

    /**
     * Get user's subscription status
     */
    public function status()
    {
        $user = Auth::user();
        $subscription = $user->activeSubscription();

        return response()->json([
            'has_subscription' => $user->hasActiveSubscription(),
            'subscription' => $subscription ? $subscription->load('plan') : null,
            'quotas' => [
                'business_cards' => [
                    'used' => $user->businessCards()->count(),
                    'remaining' => $user->getRemainingBusinessCardsQuota(),
                    'unlimited' => $subscription && $subscription->plan->max_business_cards === -1
                ],
                'club_profiles' => [
                    'used' => $user->clubProfile ? 1 : 0,
                    'remaining' => $user->getRemainingClubProfilesQuota(),
                    'unlimited' => $subscription && $subscription->plan->max_club_profiles === -1
                ]
            ]
        ]);
    }
}
