<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SubscriptionPlanAdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin');
    }

    /**
     * Display subscription plans
     */
    public function index()
    {
        $plans = SubscriptionPlan::orderBy('sort_order')->get();
        
        // Get statistics for each plan
        foreach ($plans as $plan) {
            $plan->active_subscriptions = UserSubscription::where('plan_id', $plan->_id)
                                                         ->active()
                                                         ->count();
            $plan->total_subscriptions = UserSubscription::where('plan_id', $plan->_id)->count();
            $plan->monthly_revenue = UserSubscription::where('plan_id', $plan->_id)
                                                   ->active()
                                                   ->sum('amount_paid');
        }

        return view('admin.subscription-plans.index', compact('plans'));
    }

    /**
     * Show create form
     */
    public function create()
    {
        return view('admin.subscription-plans.create');
    }

    /**
     * Store new subscription plan
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50|unique:subscription_plans,name',
            'display_name' => 'required|string|max:100',
            'description' => 'required|string|max:500',
            'monthly_price' => 'required|numeric|min:0',
            'features' => 'required|array|min:1',
            'features.*' => 'required|string|max:200',
            'permissions' => 'required|array|min:1',
            'permissions.*' => 'required|string|max:100',
            'color' => 'required|string|max:7',
            'icon' => 'required|string|max:50',
            'trial_days' => 'required|integer|min:0|max:365',
            'max_business_cards' => 'required|integer|min:-1',
            'max_club_profiles' => 'required|integer|min:-1',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        $plan = SubscriptionPlan::create([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'description' => $request->description,
            'monthly_price' => $request->monthly_price,
            'features' => array_filter($request->features),
            'permissions' => array_filter($request->permissions),
            'color' => $request->color,
            'icon' => $request->icon,
            'trial_days' => $request->trial_days,
            'max_business_cards' => $request->max_business_cards,
            'max_club_profiles' => $request->max_club_profiles,
            'sort_order' => $request->sort_order,
            'is_active' => $request->boolean('is_active', true)
        ]);

        return redirect()->route('admin.subscription-plans.index')
                       ->with('success', 'Subscription plan created successfully!');
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $plan = SubscriptionPlan::findOrFail($id);
        return view('admin.subscription-plans.edit', compact('plan'));
    }

    /**
     * Update subscription plan
     */
    public function update(Request $request, $id)
    {
        $plan = SubscriptionPlan::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50|unique:subscription_plans,name,' . $id . ',_id',
            'display_name' => 'required|string|max:100',
            'description' => 'required|string|max:500',
            'monthly_price' => 'required|numeric|min:0',
            'features' => 'required|array|min:1',
            'features.*' => 'required|string|max:200',
            'permissions' => 'required|array|min:1',
            'permissions.*' => 'required|string|max:100',
            'color' => 'required|string|max:7',
            'icon' => 'required|string|max:50',
            'trial_days' => 'required|integer|min:0|max:365',
            'max_business_cards' => 'required|integer|min:-1',
            'max_club_profiles' => 'required|integer|min:-1',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        $plan->update([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'description' => $request->description,
            'monthly_price' => $request->monthly_price,
            'features' => array_filter($request->features),
            'permissions' => array_filter($request->permissions),
            'color' => $request->color,
            'icon' => $request->icon,
            'trial_days' => $request->trial_days,
            'max_business_cards' => $request->max_business_cards,
            'max_club_profiles' => $request->max_club_profiles,
            'sort_order' => $request->sort_order,
            'is_active' => $request->boolean('is_active', true)
        ]);

        return redirect()->route('admin.subscription-plans.index')
                       ->with('success', 'Subscription plan updated successfully!');
    }

    /**
     * Delete subscription plan
     */
    public function destroy($id)
    {
        $plan = SubscriptionPlan::findOrFail($id);

        // Check if plan has active subscriptions
        $activeSubscriptions = UserSubscription::where('plan_id', $id)->active()->count();
        
        if ($activeSubscriptions > 0) {
            return redirect()->back()
                           ->with('error', "Cannot delete plan with {$activeSubscriptions} active subscriptions.");
        }

        $plan->delete();

        return redirect()->route('admin.subscription-plans.index')
                       ->with('success', 'Subscription plan deleted successfully!');
    }

    /**
     * Toggle plan status
     */
    public function toggleStatus($id)
    {
        $plan = SubscriptionPlan::findOrFail($id);
        $plan->update(['is_active' => !$plan->is_active]);

        $status = $plan->is_active ? 'activated' : 'deactivated';
        
        return response()->json([
            'status' => 'success',
            'message' => "Plan {$status} successfully!",
            'is_active' => $plan->is_active
        ]);
    }

    /**
     * Get plan statistics
     */
    public function statistics($id)
    {
        $plan = SubscriptionPlan::findOrFail($id);
        
        $stats = [
            'active_subscriptions' => UserSubscription::where('plan_id', $id)->active()->count(),
            'total_subscriptions' => UserSubscription::where('plan_id', $id)->count(),
            'trial_subscriptions' => UserSubscription::where('plan_id', $id)->trial()->count(),
            'cancelled_subscriptions' => UserSubscription::where('plan_id', $id)->cancelled()->count(),
            'monthly_revenue' => UserSubscription::where('plan_id', $id)->active()->sum('amount_paid'),
            'total_revenue' => UserSubscription::where('plan_id', $id)->sum('amount_paid')
        ];

        return response()->json($stats);
    }

    /**
     * Seed default plans
     */
    public function seedDefaults()
    {
        $defaultPlans = SubscriptionPlan::getDefaultPlans();
        
        foreach ($defaultPlans as $planData) {
            SubscriptionPlan::updateOrCreate(
                ['name' => $planData['name']],
                $planData
            );
        }

        return redirect()->route('admin.subscription-plans.index')
                       ->with('success', 'Default subscription plans seeded successfully!');
    }
}
