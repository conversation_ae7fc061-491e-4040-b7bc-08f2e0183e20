<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ClubProfile;
use App\Models\User;
use Illuminate\Support\Facades\Storage;

class ClubProfileAdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('can:admin.club-profiles.view')->only(['index', 'show']);
        $this->middleware('can:admin.club-profiles.edit')->only(['edit', 'update']);
        $this->middleware('can:admin.club-profiles.delete')->only('destroy');
        $this->middleware('can:admin.club-profiles.moderate')->only(['approve', 'reject', 'pending']);
    }

    /**
     * Display a listing of club profiles
     */
    public function index(Request $request)
    {
        $query = ClubProfile::with('user');
        
        // Filter by status
        if ($request->has('status')) {
            switch ($request->status) {
                case 'active':
                    $query->where('is_active', true);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'pending':
                    $query->where('moderation_status', 'pending');
                    break;
                case 'approved':
                    $query->where('moderation_status', 'approved');
                    break;
                case 'rejected':
                    $query->where('moderation_status', 'rejected');
                    break;
            }
        }
        
        // Search by profile name or description
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('profile_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        // Filter by tags
        if ($request->has('tag') && $request->tag) {
            $query->where('tags', 'like', "%{$request->tag}%");
        }
        
        $profiles = $query->latest()->paginate(20);
        
        return view('admin.club-profiles.index', compact('profiles'));
    }

    /**
     * Display the specified club profile
     */
    public function show($id)
    {
        $profile = ClubProfile::with('user')->findOrFail($id);
        return view('admin.club-profiles.show', compact('profile'));
    }

    /**
     * Show the form for editing the specified club profile
     */
    public function edit($id)
    {
        $profile = ClubProfile::with('user')->findOrFail($id);
        return view('admin.club-profiles.edit', compact('profile'));
    }

    /**
     * Update the specified club profile
     */
    public function update(Request $request, $id)
    {
        $profile = ClubProfile::findOrFail($id);
        
        $request->validate([
            'description' => 'nullable|string|max:2000',
            'tags' => 'nullable|string',
            'is_active' => 'boolean',
            'moderation_status' => 'required|in:pending,approved,rejected',
            'moderation_notes' => 'nullable|string|max:500'
        ]);
        
        // Process tags
        $tags = [];
        if ($request->tags) {
            $tags = array_map('trim', explode(',', $request->tags));
            $tags = array_filter($tags); // Remove empty tags
        }
        
        $profile->update([
            'description' => $request->description,
            'tags' => $tags,
            'is_active' => $request->has('is_active'),
            'is_moderated' => in_array($request->moderation_status, ['approved', 'rejected']),
            'moderation_status' => $request->moderation_status,
            'moderation_notes' => $request->moderation_notes,
            'moderated_at' => now(),
            'moderated_by' => auth()->id()
        ]);
        
        return redirect()->route('admin.club-profiles.index')
                        ->with('success', 'Club profile updated successfully!');
    }

    /**
     * Remove the specified club profile
     */
    public function destroy($id)
    {
        $profile = ClubProfile::findOrFail($id);
        
        // Delete banner image if exists
        if ($profile->banner_image && Storage::exists($profile->banner_image)) {
            Storage::delete($profile->banner_image);
        }
        
        $profile->delete();
        
        return redirect()->route('admin.club-profiles.index')
                        ->with('success', 'Club profile deleted successfully!');
    }

    /**
     * Show pending moderation profiles
     */
    public function pending()
    {
        $profiles = ClubProfile::with('user')
                              ->where('moderation_status', 'pending')
                              ->latest()
                              ->paginate(20);

        $pendingCount = ClubProfile::where('moderation_status', 'pending')->count();
        $todayApproved = ClubProfile::where('moderation_status', 'approved')
                                   ->whereDate('updated_at', today())
                                   ->count();
        $todayRejected = ClubProfile::where('moderation_status', 'rejected')
                                   ->whereDate('updated_at', today())
                                   ->count();
        $totalProfiles = ClubProfile::count();

        return view('admin.club-profiles.pending', compact('profiles', 'pendingCount', 'todayApproved', 'todayRejected', 'totalProfiles'));
    }

    /**
     * Approve a club profile
     */
    public function approve(Request $request, $id)
    {
        $profile = ClubProfile::findOrFail($id);
        
        $profile->update([
            'is_moderated' => true,
            'moderation_status' => 'approved',
            'moderation_notes' => $request->notes,
            'moderated_at' => now(),
            'moderated_by' => auth()->id()
        ]);
        
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Club profile approved successfully!'
            ]);
        }
        
        return redirect()->back()->with('success', 'Club profile approved successfully!');
    }

    /**
     * Reject a club profile
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'notes' => 'required|string|max:500'
        ]);
        
        $profile = ClubProfile::findOrFail($id);
        
        $profile->update([
            'is_moderated' => true,
            'moderation_status' => 'rejected',
            'moderation_notes' => $request->notes,
            'moderated_at' => now(),
            'moderated_by' => auth()->id()
        ]);
        
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Club profile rejected successfully!'
            ]);
        }
        
        return redirect()->back()->with('success', 'Club profile rejected successfully!');
    }

    /**
     * Toggle active status
     */
    public function toggleActive($id)
    {
        $profile = ClubProfile::findOrFail($id);
        $profile->update(['is_active' => !$profile->is_active]);
        
        return response()->json([
            'success' => true,
            'is_active' => $profile->is_active,
            'message' => 'Status updated successfully!'
        ]);
    }

    /**
     * Get statistics for dashboard
     */
    public function getStats()
    {
        return [
            'total' => ClubProfile::count(),
            'active' => ClubProfile::where('is_active', true)->count(),
            'pending' => ClubProfile::where('moderation_status', 'pending')->count(),
            'approved' => ClubProfile::where('moderation_status', 'approved')->count(),
            'rejected' => ClubProfile::where('moderation_status', 'rejected')->count(),
        ];
    }

    /**
     * Get popular tags
     */
    public function getPopularTags()
    {
        $profiles = ClubProfile::where('is_active', true)
                              ->where('moderation_status', 'approved')
                              ->get();
        
        $tagCounts = [];
        foreach ($profiles as $profile) {
            if (is_array($profile->tags)) {
                foreach ($profile->tags as $tag) {
                    $tagCounts[$tag] = ($tagCounts[$tag] ?? 0) + 1;
                }
            }
        }
        
        arsort($tagCounts);
        return array_slice($tagCounts, 0, 20, true);
    }
}
