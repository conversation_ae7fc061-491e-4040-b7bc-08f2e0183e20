<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\BusinessCard;
use App\Models\User;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

class BusinessCardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except(['show']);
    }

    /**
     * Display a listing of user's business cards
     */
    public function index()
    {
        $user = Auth::user();
        $businessCards = $user->businessCards()->orderBy('created_at', 'desc')->get();

        return view('business-cards.index', compact('businessCards'));
    }

    /**
     * Display user's business cards in dashboard
     */
    public function userIndex()
    {
        $user = Auth::user();
        $businessCards = $user->businessCards()->orderBy('created_at', 'desc')->get();

        return view('dashboard.business-cards.index', compact('businessCards'));
    }

    /**
     * Show the form for creating a new business card in dashboard
     */
    public function userCreate()
    {
        $user = Auth::user();

        // Check subscription quota
        if (!$user->canCreateBusinessCards()) {
            return redirect()->route('subscriptions.plans')
                           ->with('error', 'You have reached your business card limit or need an active subscription.')
                           ->with('show_upgrade_modal', true);
        }

        // Check if user has permission to create business cards
        if (!$user->hasPermissionTo('create_business_card')) {
            return redirect()->route('dashboard.business-cards.index')
                           ->with('error', 'You need an active subscription to create business cards. Please purchase a membership plan.');
        }

        return view('dashboard.business-cards.create');
    }

    /**
     * Store a newly created business card from dashboard
     */
    public function userStore(Request $request)
    {
        $user = Auth::user();

        // Check if user has permission to create business cards
        if (!$user->hasPermissionTo('create_business_card')) {
            return response()->json([
                'success' => false,
                'message' => 'You need an active subscription to create business cards. Please purchase a membership plan.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'profile_name' => 'required|string|max:100|unique:business_cards,profile_name|regex:/^[a-z0-9-]+$/',
            'description' => 'nullable|string|max:1000',
            'company' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:50',
            'website' => 'nullable|url|max:255',
            'location' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'social_links' => 'nullable|string|max:2000',
            'tags' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $logoPath = null;
            if ($request->hasFile('logo')) {
                $logoPath = $request->file('logo')->store('business-cards/logos', 'public');
                $logoPath = '/storage/' . $logoPath;
            }

            $businessCard = BusinessCard::create([
                'user_id' => $user->id,
                'title' => $request->title,
                'description' => $request->description,
                'profile_name' => $request->profile_name,
                'logo' => $logoPath,
                'company' => $request->company,
                'position' => $request->position,
                'email' => $request->email,
                'phone' => $request->phone,
                'website' => $request->website,
                'location' => $request->location,
                'social_links' => $request->social_links ? explode("\n", $request->social_links) : [],
                'tags' => $request->tags ? array_map('trim', explode(',', $request->tags)) : [],
                'moderation_status' => 'pending',
                'is_active' => true
            ]);

            // Generate QR code
            $this->generateQRCode($businessCard);

            return response()->json([
                'success' => true,
                'message' => 'Business card created successfully!',
                'redirect_url' => route('dashboard.business-cards.index')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create business card: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for editing a business card in dashboard
     */
    public function userEdit(BusinessCard $businessCard)
    {
        $user = Auth::user();

        // Check if user owns this business card
        if ($businessCard->user_id !== $user->id) {
            return redirect()->route('dashboard.business-cards.index')
                           ->with('error', 'You can only edit your own business cards.');
        }

        return view('dashboard.business-cards.edit', compact('businessCard'));
    }

    /**
     * Update a business card from dashboard
     */
    public function userUpdate(Request $request, BusinessCard $businessCard)
    {
        \Log::info('BusinessCard userUpdate method called', [
            'business_card_id' => $businessCard->id,
            'request_data' => $request->all(),
            'user_id' => Auth::id()
        ]);

        $user = Auth::user();

        // Check if user owns this business card
        if ($businessCard->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'You can only edit your own business cards.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'profile_name' => 'nullable|string|max:100|regex:/^[a-zA-Z0-9\-_]+$/',
            'description' => 'nullable|string|max:1000',
            'company' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:50',
            'website' => 'nullable|url|max:255',
            'location' => 'nullable|string|max:255',
            'linkedin' => 'nullable|url|max:255',
            'twitter' => 'nullable|url|max:255',
            'facebook' => 'nullable|url|max:255',
            'instagram' => 'nullable|url|max:255',
            'telegram' => 'nullable|string|max:255',
            'whatsapp' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'tags' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            \Log::info('Validation failed in userUpdate', ['errors' => $validator->errors()]);
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check profile_name uniqueness manually for MongoDB (only if profile_name is being changed)
        if ($request->profile_name && $request->profile_name !== $businessCard->profile_name) {
            $existingCard = BusinessCard::where('profile_name', $request->profile_name)
                ->where('_id', '!=', $businessCard->_id)
                ->first();
            if ($existingCard) {
                return response()->json([
                    'status' => 'error',
                    'errors' => ['profile_name' => ['The profile name has already been taken.']]
                ], 422);
            }
        }

        try {
            // Prepare contacts array from individual fields
            $contacts = [];
            if ($request->email) $contacts['email'] = $request->email;
            if ($request->phone) $contacts['phone'] = $request->phone;
            if ($request->website) $contacts['website'] = $request->website;
            if ($request->location) $contacts['location'] = $request->location;
            if ($request->linkedin) $contacts['linkedin'] = $request->linkedin;
            if ($request->twitter) $contacts['twitter'] = $request->twitter;
            if ($request->facebook) $contacts['facebook'] = $request->facebook;
            if ($request->instagram) $contacts['instagram'] = $request->instagram;
            if ($request->telegram) $contacts['telegram'] = $request->telegram;
            if ($request->whatsapp) $contacts['whatsapp'] = $request->whatsapp;

            \Log::info('Prepared contacts array for userUpdate', ['contacts' => $contacts]);

            $updateData = [
                'title' => $request->title,
                'profile_name' => $request->profile_name ?: $businessCard->profile_name,
                'description' => $request->description,
                'company' => $request->company,
                'position' => $request->position,
                'contacts' => $contacts,
                'tags' => $request->tags ? array_map('trim', explode(',', $request->tags)) : [],
                'moderation_status' => 'pending' // Reset moderation status on update
            ];

            \Log::info('Update data prepared for userUpdate', [
                'updateData' => $updateData,
                'request_company' => $request->company,
                'request_position' => $request->position,
                'request_tags' => $request->tags
            ]);

            // Handle logo upload
            if ($request->hasFile('logo')) {
                $logoPath = $request->file('logo')->store('business-cards/logos', 'public');
                $updateData['logo'] = '/storage/' . $logoPath;
            }

            $businessCard->update($updateData);

            // Log what was actually saved
            $businessCard->refresh();
            \Log::info('Business card after update', [
                'id' => $businessCard->id,
                'title' => $businessCard->title,
                'company' => $businessCard->company,
                'position' => $businessCard->position,
                'tags' => $businessCard->tags,
                'contacts' => $businessCard->contacts
            ]);

            // Regenerate QR code
            $this->generateQRCode($businessCard);

            return response()->json([
                'status' => 'success',
                'message' => 'Your business card has been updated successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update business card: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a business card from dashboard
     */
    public function userDestroy(BusinessCard $businessCard)
    {
        $user = Auth::user();

        // Check if user owns this business card
        if ($businessCard->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'You can only delete your own business cards.'
            ], 403);
        }

        try {
            $businessCard->delete();

            return response()->json([
                'success' => true,
                'message' => 'Business card deleted successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete business card: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for creating a new business card
     */
    public function create()
    {
        $user = Auth::user();

        // Check subscription quota
        if (!$user->canCreateBusinessCards()) {
            return redirect()->route('subscriptions.plans')
                           ->with('error', 'You have reached your business card limit or need an active subscription.')
                           ->with('show_upgrade_modal', true);
        }

        // Check if user can create business card
        if (!$user->canCreateBusinessCard()) {
            return redirect()->route('membership.index')
                           ->with('error', 'You need an active membership to create business cards.');
        }
        
        return view('business-cards.create');
    }

    /**
     * Store a newly created business card
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        // Check if user can create business card
        if (!$user->canCreateBusinessCard()) {
            return response()->json([
                'status' => 'error',
                'message' => 'You need an active membership to create business cards.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'profile_name' => 'nullable|string|max:100|unique:business_cards,profile_name|regex:/^[a-zA-Z0-9\-_]+$/',
            'logo' => 'nullable|string', // Base64 image
            'contacts' => 'nullable|array',
            'contacts.*.type' => 'required_with:contacts|string|in:phone,email,website,telegram,whatsapp,instagram,facebook,twitter,linkedin,youtube,tiktok,vkontakte,ok,yandex_maps,google_maps,2gis,link',
            'contacts.*.label' => 'required_with:contacts|string|max:100',
            'contacts.*.value' => 'required_with:contacts|string|max:500',
            'contacts.*.color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $businessCard = BusinessCard::create([
                'user_id' => $user->id,
                'title' => $request->title,
                'description' => $request->description,
                'profile_name' => $request->profile_name,
                'logo' => $request->logo,
                'contacts' => $request->contacts ?? [],
                'is_active' => true
            ]);

            // Generate QR code
            $this->generateQRCode($businessCard);

            return response()->json([
                'status' => 'success',
                'message' => 'Business card created successfully!',
                'redirect_url' => route('business-cards.show', $businessCard->profile_name)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error creating business card: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified business card (public view)
     */
    public function show($profileName)
    {
        $businessCard = BusinessCard::byProfileName($profileName)->active()->first();
        
        if (!$businessCard) {
            abort(404, 'Business card not found');
        }

        // Increment views count
        $businessCard->incrementViews();

        return view('business-cards.show', compact('businessCard'));
    }

    /**
     * Show the form for editing the specified business card
     */
    public function edit($id)
    {
        $user = Auth::user();
        $businessCard = BusinessCard::findOrFail($id);
        
        if (!$businessCard->canEdit($user)) {
            abort(403, 'Unauthorized to edit this business card');
        }
        
        return view('business-cards.edit', compact('businessCard'));
    }

    /**
     * Update the specified business card
     */
    public function update(Request $request, $id)
    {
        \Log::info('BusinessCard update method called', [
            'id' => $id,
            'request_data' => $request->all(),
            'user_id' => Auth::id()
        ]);

        $user = Auth::user();
        $businessCard = BusinessCard::findOrFail($id);
        
        if (!$businessCard->canEdit($user)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized to edit this business card'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'profile_name' => 'nullable|string|max:100|regex:/^[a-zA-Z0-9\-_]+$/',
            'company' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:50',
            'website' => 'nullable|url|max:255',
            'location' => 'nullable|string|max:255',
            'linkedin' => 'nullable|url|max:255',
            'twitter' => 'nullable|url|max:255',
            'facebook' => 'nullable|url|max:255',
            'instagram' => 'nullable|url|max:255',
            'telegram' => 'nullable|string|max:255',
            'whatsapp' => 'nullable|string|max:50',
            'tags' => 'nullable|string|max:500',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            \Log::info('Validation failed', ['errors' => $validator->errors()]);
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check profile_name uniqueness manually for MongoDB
        if ($request->profile_name && $request->profile_name !== $businessCard->profile_name) {
            $existingCard = BusinessCard::where('profile_name', $request->profile_name)
                ->where('_id', '!=', $businessCard->_id)
                ->first();
            if ($existingCard) {
                \Log::info('Profile name conflict', [
                    'requested_name' => $request->profile_name,
                    'current_name' => $businessCard->profile_name,
                    'existing_card_id' => $existingCard->_id,
                    'current_card_id' => $businessCard->_id
                ]);
                return response()->json([
                    'status' => 'error',
                    'errors' => ['profile_name' => ['The profile name has already been taken.']]
                ], 422);
            }
        }

        try {
            // Debug: Log request data
            \Log::info('Business card update request data', [
                'all_data' => $request->all(),
                'email' => $request->email,
                'telegram' => $request->telegram,
                'company' => $request->company,
                'position' => $request->position
            ]);

            // Handle logo upload
            $logoPath = $businessCard->logo;
            if ($request->hasFile('logo')) {
                $logoFile = $request->file('logo');
                $logoPath = $logoFile->store('business-cards/logos', 'public');
            }

            // Prepare contacts array
            $contacts = [];
            if ($request->email) $contacts['email'] = $request->email;
            if ($request->phone) $contacts['phone'] = $request->phone;
            if ($request->website) $contacts['website'] = $request->website;
            if ($request->location) $contacts['location'] = $request->location;
            if ($request->linkedin) $contacts['linkedin'] = $request->linkedin;
            if ($request->twitter) $contacts['twitter'] = $request->twitter;
            if ($request->facebook) $contacts['facebook'] = $request->facebook;
            if ($request->instagram) $contacts['instagram'] = $request->instagram;
            if ($request->telegram) $contacts['telegram'] = $request->telegram;
            if ($request->whatsapp) $contacts['whatsapp'] = $request->whatsapp;

            \Log::info('Prepared contacts array', ['contacts' => $contacts]);

            // Prepare tags array
            $tags = [];
            if ($request->tags) {
                $tags = array_map('trim', explode(',', $request->tags));
                $tags = array_filter($tags); // Remove empty tags
            }

            $businessCard->update([
                'title' => $request->title,
                'description' => $request->description,
                'profile_name' => $request->profile_name ?? $businessCard->profile_name,
                'company' => $request->company,
                'position' => $request->position,
                'logo' => $logoPath,
                'contacts' => $contacts,
                'tags' => $tags,
                'is_active' => $request->has('is_active') ? $request->is_active : $businessCard->is_active
            ]);

            // Regenerate QR code if profile_name changed
            if ($businessCard->wasChanged('profile_name')) {
                $this->generateQRCode($businessCard);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Business card updated successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error updating business card: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified business card
     */
    public function destroy($id)
    {
        $user = Auth::user();
        $businessCard = BusinessCard::findOrFail($id);
        
        if (!$businessCard->canEdit($user)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized to delete this business card'
            ], 403);
        }

        try {
            $businessCard->delete();
            
            return response()->json([
                'status' => 'success',
                'message' => 'Business card deleted successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error deleting business card: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate QR code for business card
     */
    private function generateQRCode(BusinessCard $businessCard)
    {
        try {
            $qrCode = new QrCode($businessCard->public_url);
            $qrCode->setSize(300);
            $qrCode->setMargin(10);
            
            $writer = new PngWriter();
            $result = $writer->write($qrCode);
            
            // Convert to base64
            $qrCodeBase64 = 'data:image/png;base64,' . base64_encode($result->getString());
            
            $businessCard->update(['qr_code' => $qrCodeBase64]);
            
        } catch (\Exception $e) {
            // Log error but don't fail the whole operation
            \Log::error('QR Code generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Toggle business card active status
     */
    public function toggleActive($id)
    {
        $user = Auth::user();
        $businessCard = BusinessCard::findOrFail($id);
        
        if (!$businessCard->canEdit($user)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized'
            ], 403);
        }

        $businessCard->update(['is_active' => !$businessCard->is_active]);
        
        return response()->json([
            'status' => 'success',
            'message' => 'Business card status updated',
            'is_active' => $businessCard->is_active
        ]);
    }
}
