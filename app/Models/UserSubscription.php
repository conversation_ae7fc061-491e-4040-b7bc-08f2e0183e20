<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;
use Carbon\Carbon;

class UserSubscription extends Model
{
    protected $connection = 'mongodb';
    protected $table = 'user_subscriptions';

    protected $fillable = [
        'user_id',
        'plan_id',
        'status',
        'starts_at',
        'expires_at',
        'next_billing_date',
        'auto_renew',
        'payment_method',
        'amount_paid',
        'currency',

        'cancelled_at',
        'cancellation_reason',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'next_billing_date' => 'datetime',

        'cancelled_at' => 'datetime',
        'auto_renew' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $attributes = [
        'status' => 'active',
        'auto_renew' => true,
        'currency' => 'USD'
    ];

    // Status constants
    const STATUS_ACTIVE = 'active';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_EXPIRED = 'expired';

    const STATUS_PENDING = 'pending';

    /**
     * Get the user that owns the subscription
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', '_id');
    }

    /**
     * Get the subscription plan
     */
    public function plan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id', '_id');
    }

    /**
     * Get subscription transactions
     */
    public function transactions()
    {
        return $this->hasMany(SubscriptionTransaction::class, 'subscription_id', '_id');
    }

    /**
     * Scope for active subscriptions
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE)
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope for expired subscriptions
     */
    public function scopeExpired($query)
    {
        return $query->where('status', self::STATUS_EXPIRED)
                    ->orWhere(function ($q) {
                        $q->where('expires_at', '<', now())
                          ->where('status', self::STATUS_ACTIVE);
                    });
    }



    /**
     * Scope for cancelled subscriptions
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', self::STATUS_CANCELLED);
    }

    /**
     * Scope for subscriptions expiring soon
     */
    public function scopeExpiringSoon($query, $days = 7)
    {
        return $query->where('status', self::STATUS_ACTIVE)
                    ->whereBetween('expires_at', [now(), now()->addDays($days)]);
    }

    /**
     * Check if subscription is active
     */
    public function isActive()
    {
        return $this->status === self::STATUS_ACTIVE && 
               ($this->expires_at === null || $this->expires_at > now());
    }



    /**
     * Check if subscription is expired
     */
    public function isExpired()
    {
        return $this->status === self::STATUS_EXPIRED || 
               ($this->expires_at && $this->expires_at <= now());
    }

    /**
     * Check if subscription is cancelled
     */
    public function isCancelled()
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    /**
     * Get days remaining until expiration
     */
    public function getDaysRemainingAttribute()
    {
        if (!$this->expires_at) {
            return null;
        }

        $days = now()->diffInDays($this->expires_at, false);
        return $days > 0 ? $days : 0;
    }

    /**
     * Get subscription status display
     */
    public function getStatusDisplayAttribute()
    {
        $statusLabels = [
            self::STATUS_ACTIVE => 'Active',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_EXPIRED => 'Expired',
            self::STATUS_PENDING => 'Pending'
        ];

        $status = $statusLabels[$this->status] ?? 'Unknown';

        if ($this->isActive() && $this->expires_at) {
            $status .= ' (Expires in ' . $this->days_remaining . ' days)';
        }

        return $status;
    }

    /**
     * Cancel the subscription
     */
    public function cancel($reason = null)
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
            'auto_renew' => false
        ]);

        // Remove subscriber roles
        $this->user->removeSubscriberRoles();

        return $this;
    }

    /**
     * Renew the subscription
     */
    public function renew($months = 1)
    {
        $newExpiresAt = $this->expires_at ? 
            Carbon::parse($this->expires_at)->addMonths($months) : 
            now()->addMonths($months);

        $this->update([
            'status' => self::STATUS_ACTIVE,
            'expires_at' => $newExpiresAt,
            'next_billing_date' => $newExpiresAt,
            'cancelled_at' => null,
            'cancellation_reason' => null
        ]);

        // Assign subscriber role
        if ($this->plan) {
            $this->user->assignSubscriberRole($this->plan->name);
        }

        return $this;
    }

    /**
     * Activate subscription after payment
     */
    public function activate()
    {
        $this->update([
            'status' => self::STATUS_ACTIVE,
            'starts_at' => now()
        ]);

        // Assign subscriber role
        if ($this->plan) {
            $this->user->assignSubscriberRole($this->plan->name);
        }

        return $this;
    }



    /**
     * Get formatted amount paid
     */
    public function getFormattedAmountAttribute()
    {
        return '$' . number_format($this->amount_paid, 2);
    }
}
