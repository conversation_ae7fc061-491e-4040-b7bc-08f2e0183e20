<?php

namespace App\Models;

use Illuminate\Auth\Authenticatable;
use MongoDB\Laravel\Eloquent\Model;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Maklad\Permission\Traits\HasRoles;
use Illuminate\Support\Str;

class User extends Model implements AuthenticatableContract, AuthorizableContract
{
    use Authenticatable, Authorizable, HasRoles;

    protected $connection = 'mongodb';
    protected $collection = 'users';

    protected $fillable = [
        'email', 'balance', 'two_factor_secret', 'last_seen', 'invite_code', 'invited_by', 'full_name', 'phone', 'telegram', 'birthdate', 'gender', 'avatar'
    ];

    protected $casts = ['last_seen' => 'datetime'];

    protected static function boot()
    {
        parent::boot();
        static::creating(function (self $user) {  
            $user->balance = 0;
			$user->is_verified = false;
            $user->invite_code = $user->generateInviteCode();
            $user->two_factor_secret = null;
            $user->last_seen = $user->last_seen ?? now();
        });
    }

    public function generateInviteCode()
    {
        do {
            $code = strtoupper(Str::random(8));
        } while (self::where('invite_code', $code)->exists());
        return $code;
    }

    /**
     * Get user's business cards
     */
    public function businessCards()
    {
        return $this->hasMany(BusinessCard::class, 'user_id', '_id');
    }

    /**
     * Get user's active business cards
     */
    public function activeBusinessCards()
    {
        return $this->businessCards()->active();
    }

    /**
     * Get user's club profile
     */
    public function clubProfile()
    {
        return $this->hasOne(ClubProfile::class, 'user_id', '_id');
    }





    /**
     * Assign subscriber role based on membership status
     */
    public function assignSubscriberRole($membershipStatusName)
    {
        // Remove all existing subscriber roles
        $this->removeSubscriberRoles();

        // Map membership status to role name
        $roleMapping = [
            'standard' => 'standard_subscriber',
            'privileged' => 'privileged_subscriber',
            'vip' => 'vip_subscriber',
            'god' => 'god_subscriber'
        ];

        $roleName = $roleMapping[$membershipStatusName] ?? null;

        if ($roleName) {
            $this->assignRole($roleName);
        }
    }

    /**
     * Remove all subscriber roles from user
     */
    public function removeSubscriberRoles()
    {
        $subscriberRoles = ['standard_subscriber', 'privileged_subscriber', 'vip_subscriber', 'god_subscriber'];

        foreach ($subscriberRoles as $role) {
            if ($this->hasRole($role)) {
                $this->removeRole($role);
            }
        }
    }

    /**
     * Get user's subscriptions (new system)
     */
    public function subscriptions()
    {
        return $this->hasMany(UserSubscription::class, 'user_id', '_id');
    }

    /**
     * Get user's active subscription (new system)
     */
    public function activeSubscription()
    {
        return $this->subscriptions()
                    ->with('plan')
                    ->active()
                    ->latest()
                    ->first();
    }

    /**
     * Get subscription transactions
     */
    public function subscriptionTransactions()
    {
        return $this->hasMany(SubscriptionTransaction::class, 'user_id', '_id');
    }

    /**
     * Check if user has active subscription (new system)
     */
    public function hasActiveSubscription()
    {
        $activeSubscription = $this->activeSubscription();
        return $activeSubscription && $activeSubscription->isActive();
    }

    /**
     * Get user's current subscription level (new system)
     */
    public function getSubscriptionLevel()
    {
        $activeSubscription = $this->activeSubscription();
        return $activeSubscription ? $activeSubscription->plan->name : null;
    }

    /**
     * Check if user has specific subscription plan
     */
    public function hasSubscriptionPlan($planName)
    {
        $activeSubscription = $this->activeSubscription();
        return $activeSubscription && $activeSubscription->plan->name === $planName;
    }

    /**
     * Check if user can create business cards
     */
    public function canCreateBusinessCards()
    {
        $activeSubscription = $this->activeSubscription();
        if (!$activeSubscription) {
            return false;
        }

        $plan = $activeSubscription->plan;
        $currentCount = $this->businessCards()->count();

        return $plan->max_business_cards === -1 || $currentCount < $plan->max_business_cards;
    }

    /**
     * Check if user can create club profiles
     */
    public function canCreateClubProfiles()
    {
        $activeSubscription = $this->activeSubscription();
        if (!$activeSubscription) {
            return false;
        }

        $plan = $activeSubscription->plan;
        $currentCount = $this->clubProfile ? 1 : 0;

        return $plan->max_club_profiles === -1 || $currentCount < $plan->max_club_profiles;
    }

    /**
     * Get remaining business cards quota
     */
    public function getRemainingBusinessCardsQuota()
    {
        $activeSubscription = $this->activeSubscription();
        if (!$activeSubscription) {
            return 0;
        }

        $plan = $activeSubscription->plan;
        if ($plan->max_business_cards === -1) {
            return -1; // Unlimited
        }

        $currentCount = $this->businessCards()->count();
        return max(0, $plan->max_business_cards - $currentCount);
    }

    /**
     * Get remaining club profiles quota
     */
    public function getRemainingClubProfilesQuota()
    {
        $activeSubscription = $this->activeSubscription();
        if (!$activeSubscription) {
            return 0;
        }

        $plan = $activeSubscription->plan;
        if ($plan->max_club_profiles === -1) {
            return -1; // Unlimited
        }

        $currentCount = $this->clubProfile ? 1 : 0;
        return max(0, $plan->max_club_profiles - $currentCount);
    }

    /**
     * Assign subscriber role based on subscription plan
     */
    public function assignSubscriberRoleFromPlan($planName)
    {
        // Remove all existing subscriber roles
        $this->removeSubscriberRoles();

        // Map plan name to role name
        $roleMapping = [
            'free' => null, // No role for free plan
            'basic' => 'basic_subscriber',
            'standard' => 'standard_subscriber',
            'premium' => 'premium_subscriber',
            'vip' => 'vip_subscriber'
        ];

        $roleName = $roleMapping[$planName] ?? null;

        if ($roleName) {
            $this->assignRole($roleName);
        }
    }

    /**
     * Check if user can access club members
     */
    public function canAccessClubMembers()
    {
        $membership = $this->activeMembership();
        return $membership && $membership->status && $membership->status->hasPermission('access-club-members');
    }


}
