<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;
use Illuminate\Support\Str;

class BusinessCard extends Model
{
    protected $connection = 'mongodb';
    protected $table = 'business_cards';

    protected $fillable = [
        'user_id',
        'profile_name',
        'title',
        'description',
        'company',
        'position',
        'logo',
        'contacts',
        'tags',
        'is_active',
        'qr_code',
        'views_count',
        'moderation_status',
        'moderation_notes',
        'moderated_at',
        'moderated_by',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'views_count' => 'integer',
        'moderated_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $attributes = [
        'is_active' => true,
        'views_count' => 0,
        'contacts' => [],
        'moderation_status' => 'pending'
    ];

    // Moderation status constants
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function (self $businessCard) {
            // Ensure profile_name is unique
            if (!$businessCard->profile_name) {
                $businessCard->profile_name = $businessCard->generateUniqueProfileName();
            }
            
            // Initialize default values
            $businessCard->is_active = $businessCard->is_active ?? true;
            $businessCard->views_count = 0;
            $businessCard->contacts = $businessCard->contacts ?? [];
        });
        
        static::updating(function (self $businessCard) {
            // Regenerate QR code if profile_name changed
            if ($businessCard->isDirty('profile_name')) {
                $businessCard->qr_code = null; // Will be regenerated
            }
        });
    }

    /**
     * Generate unique profile name for business card
     */
    public function generateUniqueProfileName()
    {
        $baseSlug = Str::slug($this->title ?? 'card');
        $slug = $baseSlug;
        $counter = 1;

        while (self::where('profile_name', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get the user that owns the business card
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', '_id');
    }

    /**
     * Get the public URL for this business card
     */
    public function getPublicUrlAttribute()
    {
        return url('/tag/' . $this->profile_name);
    }

    /**
     * Increment views count
     */
    public function incrementViews()
    {
        $this->increment('views_count');
    }

    /**
     * Check if user can edit this business card
     */
    public function canEdit($user)
    {
        return $user && $this->user_id === $user->id;
    }

    /**
     * Get formatted contacts for display
     */
    public function getFormattedContactsAttribute()
    {
        $formatted = [];
        
        foreach ($this->contacts as $contact) {
            $formatted[] = [
                'type' => $contact['type'] ?? 'link',
                'label' => $contact['label'] ?? '',
                'value' => $contact['value'] ?? '',
                'color' => $contact['color'] ?? '#007bff',
                'icon' => $this->getContactIcon($contact['type'] ?? 'link')
            ];
        }
        
        return $formatted;
    }

    /**
     * Get icon for contact type
     */
    private function getContactIcon($type)
    {
        $icons = [
            'phone' => 'phone',
            'email' => 'envelope',
            'website' => 'globe',
            'telegram' => 'paper-plane',
            'whatsapp' => 'whatsapp',
            'instagram' => 'instagram',
            'facebook' => 'facebook',
            'twitter' => 'twitter',
            'linkedin' => 'linkedin',
            'youtube' => 'youtube',
            'tiktok' => 'music',
            'vkontakte' => 'vk',
            'ok' => 'odnoklassniki',
            'yandex_maps' => 'map-marker',
            'google_maps' => 'map-marker',
            '2gis' => 'map-marker',
            'link' => 'link'
        ];

        return $icons[$type] ?? 'link';
    }

    /**
     * Scope for active business cards
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('moderation_status', 'approved');
    }

    /**
     * Scope for finding by profile name
     */
    public function scopeByProfileName($query, $profileName)
    {
        return $query->where('profile_name', $profileName);
    }

    /**
     * Scope for approved business cards
     */
    public function scopeApproved($query)
    {
        return $query->where('moderation_status', self::STATUS_APPROVED);
    }

    /**
     * Scope for pending moderation
     */
    public function scopePending($query)
    {
        return $query->where('moderation_status', self::STATUS_PENDING);
    }

    /**
     * Scope for rejected business cards
     */
    public function scopeRejected($query)
    {
        return $query->where('moderation_status', self::STATUS_REJECTED);
    }

    /**
     * Check if business card is approved
     */
    public function isApproved()
    {
        return $this->moderation_status === self::STATUS_APPROVED;
    }

    /**
     * Check if business card is pending
     */
    public function isPending()
    {
        return $this->moderation_status === self::STATUS_PENDING;
    }

    /**
     * Check if business card is rejected
     */
    public function isRejected()
    {
        return $this->moderation_status === self::STATUS_REJECTED;
    }

    /**
     * Approve the business card
     */
    public function approve($notes = null, $moderatorId = null)
    {
        $this->update([
            'moderation_status' => self::STATUS_APPROVED,
            'moderation_notes' => $notes,
            'moderated_at' => now(),
            'moderated_by' => $moderatorId ?? auth()->id()
        ]);
    }

    /**
     * Reject the business card
     */
    public function reject($notes, $moderatorId = null)
    {
        $this->update([
            'moderation_status' => self::STATUS_REJECTED,
            'moderation_notes' => $notes,
            'moderated_at' => now(),
            'moderated_by' => $moderatorId ?? auth()->id()
        ]);
    }

    /**
     * Get moderator who moderated this card
     */
    public function moderator()
    {
        return $this->belongsTo(User::class, 'moderated_by', '_id');
    }

    /**
     * Get moderation status badge HTML
     */
    public function getModerationBadgeAttribute()
    {
        $badges = [
            self::STATUS_PENDING => '<span class="badge badge-warning">Pending</span>',
            self::STATUS_APPROVED => '<span class="badge badge-success">Approved</span>',
            self::STATUS_REJECTED => '<span class="badge badge-danger">Rejected</span>',
        ];

        return $badges[$this->moderation_status] ?? '<span class="badge badge-secondary">Unknown</span>';
    }
}
