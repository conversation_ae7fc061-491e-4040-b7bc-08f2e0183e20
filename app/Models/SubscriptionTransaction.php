<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class SubscriptionTransaction extends Model
{
    protected $connection = 'mongodb';
    protected $table = 'subscription_transactions';

    protected $fillable = [
        'user_id',
        'subscription_id',
        'plan_id',
        'type',
        'status',
        'amount',
        'currency',
        'payment_method',
        'payment_data',
        'description',
        'processed_at',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'processed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $attributes = [
        'currency' => 'USD',
        'status' => 'pending'
    ];

    // Transaction types
    const TYPE_SUBSCRIPTION = 'subscription';
    const TYPE_RENEWAL = 'renewal';
    const TYPE_UPGRADE = 'upgrade';
    const TYPE_DOWNGRADE = 'downgrade';
    const TYPE_REFUND = 'refund';


    // Transaction statuses
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';

    /**
     * Get the user that owns the transaction
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', '_id');
    }

    /**
     * Get the subscription
     */
    public function subscription()
    {
        return $this->belongsTo(UserSubscription::class, 'subscription_id', '_id');
    }

    /**
     * Get the subscription plan
     */
    public function plan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id', '_id');
    }

    /**
     * Scope for completed transactions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for pending transactions
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for failed transactions
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * Scope for specific transaction type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Mark transaction as completed
     */
    public function markCompleted($paymentData = [])
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'processed_at' => now(),
            'payment_data' => array_merge($this->payment_data ?? [], $paymentData)
        ]);

        return $this;
    }

    /**
     * Mark transaction as failed
     */
    public function markFailed($reason = null)
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'processed_at' => now(),
            'payment_data' => array_merge($this->payment_data ?? [], [
                'failure_reason' => $reason
            ])
        ]);

        return $this;
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute()
    {
        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get transaction type display
     */
    public function getTypeDisplayAttribute()
    {
        $typeLabels = [
            self::TYPE_SUBSCRIPTION => 'New Subscription',
            self::TYPE_RENEWAL => 'Subscription Renewal',
            self::TYPE_UPGRADE => 'Plan Upgrade',
            self::TYPE_DOWNGRADE => 'Plan Downgrade',
            self::TYPE_REFUND => 'Refund',

        ];

        return $typeLabels[$this->type] ?? 'Unknown';
    }

    /**
     * Get transaction status display
     */
    public function getStatusDisplayAttribute()
    {
        $statusLabels = [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_REFUNDED => 'Refunded'
        ];

        return $statusLabels[$this->status] ?? 'Unknown';
    }

    /**
     * Create a new subscription transaction
     */
    public static function createSubscriptionTransaction($userId, $subscriptionId, $planId, $amount, $type = self::TYPE_SUBSCRIPTION)
    {
        return self::create([
            'user_id' => $userId,
            'subscription_id' => $subscriptionId,
            'plan_id' => $planId,
            'type' => $type,
            'amount' => $amount,
            'description' => "Subscription transaction for plan"
        ]);
    }

    /**
     * Create a renewal transaction
     */
    public static function createRenewalTransaction($userId, $subscriptionId, $planId, $amount)
    {
        return self::createSubscriptionTransaction($userId, $subscriptionId, $planId, $amount, self::TYPE_RENEWAL);
    }

    /**
     * Create an upgrade transaction
     */
    public static function createUpgradeTransaction($userId, $subscriptionId, $planId, $amount)
    {
        return self::createSubscriptionTransaction($userId, $subscriptionId, $planId, $amount, self::TYPE_UPGRADE);
    }


}
