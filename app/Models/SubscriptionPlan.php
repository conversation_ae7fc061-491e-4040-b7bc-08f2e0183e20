<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;
use Carbon\Carbon;

class SubscriptionPlan extends Model
{
    protected $connection = 'mongodb';
    protected $table = 'subscription_plans';

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'monthly_price',
        'features',
        'permissions',
        'color',
        'icon',
        'is_active',
        'sort_order',

        'max_business_cards',
        'max_club_profiles',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'monthly_price' => 'float',
        'is_active' => 'boolean',
        'sort_order' => 'integer',

        'max_business_cards' => 'integer',
        'max_club_profiles' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $attributes = [
        'is_active' => true,
        'sort_order' => 0,
        'color' => '#007bff',
        'max_business_cards' => 1,
        'max_club_profiles' => 1
    ];

    // Plan constants
    const PLAN_FREE = 'free';
    const PLAN_BASIC = 'basic';
    const PLAN_STANDARD = 'standard';
    const PLAN_PREMIUM = 'premium';
    const PLAN_VIP = 'vip';

    /**
     * Get active subscription plans
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get plans ordered by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc');
    }

    /**
     * Get plan by name
     */
    public function scopeByName($query, $name)
    {
        return $query->where('name', $name);
    }

    /**
     * Get user subscriptions for this plan
     */
    public function subscriptions()
    {
        return $this->hasMany(UserSubscription::class, 'plan_id', '_id');
    }

    /**
     * Get active subscriptions count
     */
    public function getActiveSubscriptionsCountAttribute()
    {
        return $this->subscriptions()->active()->count();
    }

    /**
     * Check if plan has specific permission
     */
    public function hasPermission($permission)
    {
        return in_array($permission, $this->permissions ?? []);
    }

    /**
     * Get monthly price as float (handle MongoDB Decimal128)
     */
    public function getMonthlyPriceAttribute($value)
    {
        if ($value instanceof \MongoDB\BSON\Decimal128) {
            return (float) $value->__toString();
        }
        return (float) $value;
    }

    /**
     * Get formatted monthly price
     */
    public function getFormattedPriceAttribute()
    {
        return '$' . number_format($this->monthly_price, 2);
    }

    /**
     * Get plan badge HTML
     */
    public function getBadgeHtmlAttribute()
    {
        return '<span class="plan-badge" style="background-color: ' . $this->color . '">' . $this->display_name . '</span>';
    }

    /**
     * Check if this is a free plan
     */
    public function isFree()
    {
        return $this->monthly_price == 0;
    }

    /**
     * Get default subscription plans for seeding
     */
    public static function getDefaultPlans()
    {
        return [
            [
                'name' => self::PLAN_FREE,
                'display_name' => 'Free',
                'description' => 'Basic access with limited features',
                'monthly_price' => 0.00,
                'features' => [
                    'View public profiles',
                    'Basic search functionality'
                ],
                'permissions' => ['view_public_profiles'],
                'color' => '#6c757d',
                'icon' => 'user',
                'sort_order' => 1,

                'max_business_cards' => 0,
                'max_club_profiles' => 0
            ],
            [
                'name' => self::PLAN_BASIC,
                'display_name' => 'Basic',
                'description' => 'Perfect for individuals getting started',
                'monthly_price' => 9.99,
                'features' => [
                    '1 Business Card',
                    '1 Club Profile',
                    'Basic customization',
                    'Email support'
                ],
                'permissions' => ['create_business_card', 'create_club_profile', 'basic_features'],
                'color' => '#17a2b8',
                'icon' => 'user-plus',
                'sort_order' => 2,

                'max_business_cards' => 1,
                'max_club_profiles' => 1
            ],
            [
                'name' => self::PLAN_STANDARD,
                'display_name' => 'Standard',
                'description' => 'Great for professionals and small businesses',
                'monthly_price' => 19.99,
                'features' => [
                    '3 Business Cards',
                    '2 Club Profiles',
                    'Advanced customization',
                    'Priority support',
                    'Analytics dashboard'
                ],
                'permissions' => ['create_business_card', 'create_club_profile', 'advanced_features', 'analytics'],
                'color' => '#28a745',
                'icon' => 'briefcase',
                'sort_order' => 3,

                'max_business_cards' => 3,
                'max_club_profiles' => 2
            ],
            [
                'name' => self::PLAN_PREMIUM,
                'display_name' => 'Premium',
                'description' => 'For growing businesses and teams',
                'monthly_price' => 39.99,
                'features' => [
                    '10 Business Cards',
                    '5 Club Profiles',
                    'Premium customization',
                    'Priority support',
                    'Advanced analytics',
                    'Team collaboration'
                ],
                'permissions' => ['create_business_card', 'create_club_profile', 'premium_features', 'analytics', 'team_features'],
                'color' => '#ffc107',
                'icon' => 'star',
                'sort_order' => 4,

                'max_business_cards' => 10,
                'max_club_profiles' => 5
            ],
            [
                'name' => self::PLAN_VIP,
                'display_name' => 'VIP',
                'description' => 'Ultimate plan with unlimited access',
                'monthly_price' => 99.99,
                'features' => [
                    'Unlimited Business Cards',
                    'Unlimited Club Profiles',
                    'VIP customization',
                    '24/7 VIP support',
                    'Advanced analytics',
                    'Team collaboration',
                    'White-label options',
                    'API access'
                ],
                'permissions' => ['create_business_card', 'create_club_profile', 'vip_features', 'analytics', 'team_features', 'api_access'],
                'color' => '#dc3545',
                'icon' => 'crown',
                'sort_order' => 5,

                'max_business_cards' => -1, // Unlimited
                'max_club_profiles' => -1   // Unlimited
            ]
        ];
    }
}
