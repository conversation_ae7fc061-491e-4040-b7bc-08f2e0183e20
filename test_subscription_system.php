<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use App\Models\SubscriptionTransaction;
use Illuminate\Support\Facades\Hash;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== SUBSCRIPTION SYSTEM TEST ===\n\n";

// 1. Check if subscription plans exist
echo "1. Checking subscription plans...\n";
$plans = SubscriptionPlan::all();
echo "Found " . $plans->count() . " subscription plans:\n";
foreach ($plans as $plan) {
    echo "- {$plan->display_name}: \${$plan->monthly_price}/month (max cards: {$plan->max_business_cards}, max profiles: {$plan->max_club_profiles})\n";
}
echo "\n";

// 2. Create or find test user
echo "2. Creating/finding test user...\n";
$testUser = User::where('email', '<EMAIL>')->first();
if (!$testUser) {
    $testUser = User::create([
        'email' => '<EMAIL>',
        'name' => 'Test User',
        'password' => Hash::make('password'),
        'balance' => 1000.00,
        'is_verified' => true,
        'verification_code' => '123456'
    ]);
    echo "Created test user: {$testUser->email}\n";
} else {
    echo "Found existing test user: {$testUser->email}\n";
    // Update balance for testing
    $testUser->balance = 1000.00;
    $testUser->save();
}
echo "User balance: \${$testUser->balance}\n\n";

// 3. Test subscription methods
echo "3. Testing subscription methods...\n";
echo "Has active subscription: " . ($testUser->hasActiveSubscription() ? 'Yes' : 'No') . "\n";
echo "Can create business cards: " . ($testUser->canCreateBusinessCards() ? 'Yes' : 'No') . "\n";
echo "Can create club profiles: " . ($testUser->canCreateClubProfiles() ? 'Yes' : 'No') . "\n";

$activeSubscription = $testUser->activeSubscription();
if ($activeSubscription) {
    echo "Current plan: {$activeSubscription->plan->display_name}\n";
    echo "Expires at: {$activeSubscription->expires_at}\n";
} else {
    echo "No active subscription\n";
}
echo "\n";

// 4. Test subscription purchase
echo "4. Testing subscription purchase...\n";
$basicPlan = SubscriptionPlan::where('name', 'basic')->first();
if ($basicPlan && !$testUser->hasActiveSubscription()) {
    echo "Purchasing Basic plan for test user...\n";
    
    // Cancel existing subscription if any
    $existingSubscription = $testUser->activeSubscription();
    if ($existingSubscription) {
        $existingSubscription->cancel('Upgraded to new plan');
        echo "Cancelled existing subscription\n";
    }
    
    // Check balance
    if ($testUser->balance < $basicPlan->monthly_price) {
        echo "Insufficient balance! User has \${$testUser->balance}, plan costs \${$basicPlan->monthly_price}\n";
    } else {
        // Deduct balance
        $testUser->balance -= $basicPlan->monthly_price;
        $testUser->save();
        
        // Create subscription
        $subscription = UserSubscription::create([
            'user_id' => $testUser->_id,
            'plan_id' => $basicPlan->_id,
            'status' => UserSubscription::STATUS_ACTIVE,
            'starts_at' => now(),
            'expires_at' => now()->addMonth(),
            'next_billing_date' => now()->addMonth(),
            'auto_renew' => true,
            'payment_method' => 'balance',
            'amount_paid' => $basicPlan->monthly_price,
            'currency' => 'USD'
        ]);
        
        // Create transaction
        $transaction = SubscriptionTransaction::createSubscriptionTransaction(
            $testUser->_id,
            $subscription->_id,
            $basicPlan->monthly_price,
            'balance'
        );
        
        // Assign subscriber role
        $testUser->assignSubscriberRoleFromPlan($basicPlan);
        
        echo "Successfully purchased Basic plan!\n";
        echo "New balance: \${$testUser->balance}\n";
        echo "Subscription expires: {$subscription->expires_at}\n";
    }
} else {
    echo "Basic plan not found or user already has active subscription\n";
}
echo "\n";

// 5. Test updated subscription methods
echo "5. Testing updated subscription methods...\n";
$testUser->refresh();
echo "Has active subscription: " . ($testUser->hasActiveSubscription() ? 'Yes' : 'No') . "\n";
echo "Can create business cards: " . ($testUser->canCreateBusinessCards() ? 'Yes' : 'No') . "\n";
echo "Can create club profiles: " . ($testUser->canCreateClubProfiles() ? 'Yes' : 'No') . "\n";

$activeSubscription = $testUser->activeSubscription();
if ($activeSubscription) {
    echo "Current plan: {$activeSubscription->plan->display_name}\n";
    echo "Business cards quota: " . $testUser->getRemainingBusinessCardsQuota() . " remaining\n";
    echo "Club profiles quota: " . $testUser->getRemainingClubProfilesQuota() . " remaining\n";
}
echo "\n";

// 6. Test transaction history
echo "6. Checking transaction history...\n";
$transactions = SubscriptionTransaction::where('user_id', $testUser->_id)->get();
echo "Found " . $transactions->count() . " transactions:\n";
foreach ($transactions as $transaction) {
    echo "- {$transaction->type}: \${$transaction->amount} ({$transaction->status}) - {$transaction->created_at}\n";
}
echo "\n";

echo "=== TEST COMPLETED ===\n";
