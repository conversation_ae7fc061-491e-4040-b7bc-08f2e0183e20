<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Creating test session...\n";

// Find test user
$testUser = User::where('email', '<EMAIL>')->first();
if (!$testUser) {
    echo "Test user not found!\n";
    exit(1);
}

echo "Found test user: {$testUser->email}\n";

// Create session data
$sessionData = [
    'user_id' => (string)$testUser->_id,
    'email' => $testUser->email,
    'name' => $testUser->name,
    'is_verified' => true,
    'login_at' => now()->toISOString()
];

// Save to file for manual session creation
file_put_contents('test_session.json', json_encode($sessionData, JSON_PRETTY_PRINT));

echo "Session data saved to test_session.json\n";
echo "User ID: {$testUser->_id}\n";
echo "Email: {$testUser->email}\n";
echo "Balance: \${$testUser->balance}\n";

// Check subscription status
$activeSubscription = $testUser->activeSubscription();
if ($activeSubscription) {
    echo "Active subscription: {$activeSubscription->plan->display_name}\n";
    echo "Expires: {$activeSubscription->expires_at}\n";
} else {
    echo "No active subscription\n";
}

echo "Done!\n";
