<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Maklad\Permission\Models\Role;
use Maklad\Permission\Models\Permission;

class SubscriberRolesSeeder extends Seeder
{
    public function run()
    {
        // Create subscriber roles
        $roles = [
            [
                'name' => 'basic_subscriber',
                'display_name' => 'Basic Subscriber',
                'description' => 'Basic membership subscriber with limited access'
            ],
            [
                'name' => 'standard_subscriber',
                'display_name' => 'Standard Subscriber',
                'description' => 'Standard membership subscriber with basic access'
            ],
            [
                'name' => 'premium_subscriber',
                'display_name' => 'Premium Subscriber',
                'description' => 'Premium membership subscriber with enhanced access'
            ],
            [
                'name' => 'vip_subscriber',
                'display_name' => 'VIP Subscriber',
                'description' => 'VIP membership subscriber with premium access'
            ]
        ];

        foreach ($roles as $roleData) {
            Role::firstOrCreate(
                ['name' => $roleData['name']],
                [
                    'display_name' => $roleData['display_name'],
                    'description' => $roleData['description']
                ]
            );
        }

        // Create subscriber permissions
        $permissions = [
            [
                'name' => 'create_business_card',
                'display_name' => 'Create Business Card',
                'description' => 'Permission to create business cards'
            ],
            [
                'name' => 'create_club_profile',
                'display_name' => 'Create Club Profile',
                'description' => 'Permission to create club profiles'
            ],
            [
                'name' => 'unlimited_business_cards',
                'display_name' => 'Unlimited Business Cards',
                'description' => 'Permission to create unlimited business cards'
            ],
            [
                'name' => 'priority_moderation',
                'display_name' => 'Priority Moderation',
                'description' => 'Priority moderation for content'
            ],
            [
                'name' => 'advanced_analytics',
                'display_name' => 'Advanced Analytics',
                'description' => 'Access to advanced analytics and statistics'
            ]
        ];

        foreach ($permissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                [
                    'display_name' => $permissionData['display_name'],
                    'description' => $permissionData['description']
                ]
            );
        }

        // Assign permissions to roles
        $rolePermissions = [
            'basic_subscriber' => [
                'create_business_card',
                'create_club_profile'
            ],
            'standard_subscriber' => [
                'create_business_card',
                'create_club_profile'
            ],
            'premium_subscriber' => [
                'create_business_card',
                'create_club_profile',
                'unlimited_business_cards'
            ],
            'vip_subscriber' => [
                'create_business_card',
                'create_club_profile',
                'unlimited_business_cards',
                'priority_moderation',
                'advanced_analytics'
            ]
        ];

        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::where('name', $roleName)->first();
            if ($role) {
                $role->syncPermissions($permissions);
            }
        }

        $this->command->info('Subscriber roles and permissions created successfully!');
    }
}
