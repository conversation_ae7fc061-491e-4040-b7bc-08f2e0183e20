<?php $__env->startSection('title'); ?>
Business Cards - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <!-- Start datatable css -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.19/css/jquery.dataTables.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.18/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap.min.css">
    <style>
        .card-logo {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
        }
        .status-toggle {
            cursor: pointer;
        }
        .moderation-actions {
            white-space: nowrap;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<!-- Page Title Area Start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Business Cards</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><span>Business Cards</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- Page Title Area End -->

<div class="main-content-inner">
    <div class="row">
        <!-- Filter Cards Start -->
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Filter Business Cards</h4>
                    <form method="GET" action="<?php echo e(route('admin.business-cards.index')); ?>" class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Status</label>
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                    <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                    <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>Approved</option>
                                    <option value="rejected" <?php echo e(request('status') == 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Search</label>
                                <input type="text" name="search" class="form-control" placeholder="Search by title, profile name or description..." value="<?php echo e(request('search')); ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">Filter</button>
                                    <a href="<?php echo e(route('admin.business-cards.index')); ?>" class="btn btn-secondary">Clear</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- Filter Cards End -->

        <!-- Data Table Start -->
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title float-left">Business Cards List</h4>
                    <p class="float-right mb-2">
                        <a class="btn btn-warning text-white" href="<?php echo e(route('admin.business-cards.pending')); ?>">
                            <i class="fa fa-clock-o"></i> Pending Moderation
                        </a>
                    </p>
                    <div class="clearfix"></div>
                    <div class="data-tables">
                        <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <table id="dataTable" class="text-center">
                            <thead class="bg-light text-capitalize">
                                <tr>
                                    <th>Logo</th>
                                    <th>Title</th>
                                    <th>Profile Name</th>
                                    <th>User</th>
                                    <th>Status</th>
                                    <th>Moderation</th>
                                    <th>Views</th>
                                    <th>Created</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $cards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <?php if($card->logo): ?>
                                            <img src="<?php echo e(Storage::url($card->logo)); ?>" alt="Logo" class="card-logo">
                                        <?php else: ?>
                                            <div class="card-logo bg-light d-flex align-items-center justify-content-center">
                                                <i class="fa fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo e($card->title); ?></strong>
                                        <?php if($card->description): ?>
                                            <br><small class="text-muted"><?php echo e(Str::limit($card->description, 50)); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo e($card->public_url); ?>" target="_blank" class="text-primary">
                                            <?php echo e($card->profile_name); ?>

                                        </a>
                                    </td>
                                    <td>
                                        <?php echo e($card->user->name ?? 'N/A'); ?>

                                        <br><small class="text-muted"><?php echo e($card->user->email ?? 'N/A'); ?></small>
                                    </td>
                                    <td>
                                        <span class="status-toggle" data-id="<?php echo e($card->_id); ?>">
                                            <?php if($card->is_active): ?>
                                                <span class="badge badge-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge badge-secondary">Inactive</span>
                                            <?php endif; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo $card->moderation_badge; ?>

                                        <?php if($card->moderation_notes): ?>
                                            <br><small class="text-muted"><?php echo e(Str::limit($card->moderation_notes, 30)); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e(number_format($card->views_count)); ?></td>
                                    <td><?php echo e($card->created_at->format('M d, Y')); ?></td>
                                    <td class="moderation-actions">
                                        <a class="btn btn-info btn-sm text-white" href="<?php echo e(route('admin.business-cards.show', $card->_id)); ?>">
                                            <i class="fa fa-eye"></i>
                                        </a>
                                        <a class="btn btn-success btn-sm text-white" href="<?php echo e(route('admin.business-cards.edit', $card->_id)); ?>">
                                            <i class="fa fa-edit"></i>
                                        </a>
                                        <?php if($card->isPending()): ?>
                                            <button class="btn btn-primary btn-sm approve-btn" data-id="<?php echo e($card->_id); ?>">
                                                <i class="fa fa-check"></i>
                                            </button>
                                            <button class="btn btn-danger btn-sm reject-btn" data-id="<?php echo e($card->_id); ?>">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        <?php endif; ?>
                                        <button class="btn btn-danger btn-sm delete-btn" data-id="<?php echo e($card->_id); ?>">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                        
                        <!-- Pagination -->
                        <div class="mt-3">
                            <?php echo e($cards->appends(request()->query())->links()); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Data Table End -->
    </div>
</div>

<!-- Moderation Modal -->
<div class="modal fade" id="moderationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Moderate Business Card</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="moderationForm">
                    <input type="hidden" id="cardId">
                    <input type="hidden" id="action">
                    <div class="form-group">
                        <label>Notes</label>
                        <textarea class="form-control" id="notes" rows="3" placeholder="Add moderation notes..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmModeration">Confirm</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <!-- Start datatable js -->
    <script src="https://cdn.datatables.net/1.10.19/js/jquery.dataTables.js"></script>
    <script src="https://cdn.datatables.net/1.10.18/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.18/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#dataTable').DataTable({
                responsive: true,
                autoWidth: false,
                paging: false,
                info: false,
                searching: false
            });

            // Status toggle
            $('.status-toggle').click(function() {
                const cardId = $(this).data('id');
                const element = $(this);
                
                $.post(`/admin/business-cards/${cardId}/toggle-active`, {
                    _token: '<?php echo e(csrf_token()); ?>'
                }).done(function(response) {
                    if (response.success) {
                        if (response.is_active) {
                            element.html('<span class="badge badge-success">Active</span>');
                        } else {
                            element.html('<span class="badge badge-secondary">Inactive</span>');
                        }
                    }
                });
            });

            // Approve button
            $('.approve-btn').click(function() {
                const cardId = $(this).data('id');
                $('#cardId').val(cardId);
                $('#action').val('approve');
                $('.modal-title').text('Approve Business Card');
                $('#notes').attr('placeholder', 'Add approval notes (optional)...');
                $('#moderationModal').modal('show');
            });

            // Reject button
            $('.reject-btn').click(function() {
                const cardId = $(this).data('id');
                $('#cardId').val(cardId);
                $('#action').val('reject');
                $('.modal-title').text('Reject Business Card');
                $('#notes').attr('placeholder', 'Add rejection reason (required)...');
                $('#moderationModal').modal('show');
            });

            // Confirm moderation
            $('#confirmModeration').click(function() {
                const cardId = $('#cardId').val();
                const action = $('#action').val();
                const notes = $('#notes').val();
                
                if (action === 'reject' && !notes.trim()) {
                    alert('Rejection reason is required');
                    return;
                }
                
                $.post(`/admin/business-cards/${cardId}/${action}`, {
                    _token: '<?php echo e(csrf_token()); ?>',
                    notes: notes
                }).done(function(response) {
                    if (response.success) {
                        location.reload();
                    }
                });
            });

            // Delete button
            $('.delete-btn').click(function() {
                if (confirm('Are you sure you want to delete this business card?')) {
                    const cardId = $(this).data('id');
                    const form = $('<form method="POST" action="/admin/business-cards/' + cardId + '">' +
                        '<input type="hidden" name="_token" value="<?php echo e(csrf_token()); ?>">' +
                        '<input type="hidden" name="_method" value="DELETE">' +
                        '</form>');
                    $('body').append(form);
                    form.submit();
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/business-cards/index.blade.php ENDPATH**/ ?>