<!DOCTYPE html>
<html data-wf-domain="/" data-wf-page="663d095d8e07618c5381377b" data-wf-site="663b748d327c826a2952af46" data-wf-status="1" lang="en" class=" w-mod-js w-mod-ix">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Subscription Plans - EVOLUTION888</title>

  <!-- CSRF Token (Laravel) -->
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <!-- Main CSS -->
  <link rel="stylesheet" href="<?php echo e(asset('css/style.css')); ?>" />
  <link href="/css/techbetatemplates.webflow.14600f26e.css?v=0.3" rel="stylesheet" type="text/css">

  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

  <style>
    .subscription-hero {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 100px 0;
      text-align: center;
      color: white;
      position: relative;
      overflow: hidden;
    }

    .subscription-hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
    }

    .subscription-hero h1 {
      font-size: 4rem;
      font-weight: 800;
      margin-bottom: 25px;
      text-shadow: 0 4px 8px rgba(0,0,0,0.3);
      background: linear-gradient(45deg, #fff, #f0f8ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      position: relative;
      z-index: 1;
    }

    .subscription-hero p {
      font-size: 1.4rem;
      opacity: 0.95;
      max-width: 700px;
      margin: 0 auto;
      line-height: 1.6;
      position: relative;
      z-index: 1;
    }

    .plans-container {
      max-width: 1400px;
      margin: -80px auto 100px;
      padding: 0 20px;
      position: relative;
      z-index: 10;
    }

    .plans-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 40px;
      margin-top: 60px;
    }

    .plan-card {
      background: linear-gradient(145deg, #ffffff, #f8f9fa);
      border-radius: 24px;
      box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.08),
        0 8px 25px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
      overflow: hidden;
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      position: relative;
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
    }

    .plan-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
      background-size: 300% 100%;
      animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    .plan-card:hover {
      transform: translateY(-15px) scale(1.02);
      box-shadow:
        0 30px 80px rgba(0, 0, 0, 0.12),
        0 15px 40px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    .plan-card.current-plan {
      background: linear-gradient(145deg, #e8f5e8, #f0f8f0);
      border: 2px solid #28a745;
      transform: scale(1.05);
      box-shadow:
        0 25px 70px rgba(40, 167, 69, 0.15),
        0 10px 30px rgba(40, 167, 69, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    .plan-card.current-plan::before {
      background: linear-gradient(90deg, #28a745, #20c997, #28a745);
    }

    .plan-card.popular {
      background: linear-gradient(145deg, #fff8e1, #fffbf0);
      border: 2px solid #ffc107;
      position: relative;
      transform: scale(1.08);
      box-shadow:
        0 30px 80px rgba(255, 193, 7, 0.2),
        0 15px 40px rgba(255, 193, 7, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    .plan-card.popular::before {
      background: linear-gradient(90deg, #ffc107, #ffeb3b, #ff9800, #ffc107);
    }

    .plan-card.popular::after {
      content: 'MOST POPULAR';
      position: absolute;
      top: 25px;
      right: -35px;
      background: linear-gradient(135deg, #ffc107, #ffb300);
      color: #000;
      padding: 10px 45px;
      font-size: 0.75rem;
      font-weight: 800;
      text-transform: uppercase;
      letter-spacing: 1px;
      transform: rotate(45deg);
      z-index: 5;
      box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
    }

    .current-plan-badge {
      position: absolute;
      top: 25px;
      left: 25px;
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      padding: 10px 20px;
      border-radius: 25px;
      font-size: 0.75rem;
      font-weight: 800;
      text-transform: uppercase;
      letter-spacing: 1px;
      box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .plan-header {
      padding: 50px 35px 35px;
      text-align: center;
      position: relative;
    }

    .plan-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 25px;
      font-size: 32px;
      color: white;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      position: relative;
      overflow: hidden;
    }

    .plan-icon::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
      transform: rotate(45deg);
      animation: iconShine 2s ease-in-out infinite;
    }

    @keyframes iconShine {
      0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
      50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .plan-name {
      font-size: 2.2rem;
      font-weight: 800;
      margin-bottom: 15px;
      background: linear-gradient(135deg, #2c3e50, #34495e);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .plan-description {
      color: #6c757d;
      font-size: 1.1rem;
      margin-bottom: 25px;
      line-height: 1.5;
    }

    .plan-price {
      font-size: 3.2rem;
      font-weight: 900;
      background: linear-gradient(135deg, #2c3e50, #34495e);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 10px;
      position: relative;
      line-height: 1.1;
      display: flex;
      align-items: baseline;
      justify-content: center;
      flex-wrap: wrap;
      gap: 2px;
    }

    .plan-price .currency {
      font-size: 1.6rem;
      vertical-align: top;
    }

    .plan-price .period {
      font-size: 1rem;
      color: #6c757d;
      font-weight: 500;
      white-space: nowrap;
    }

    /* Responsive price sizing */
    .plan-price.large-price {
      font-size: 2.8rem;
    }

    .plan-price.large-price .currency {
      font-size: 1.4rem;
    }

    .plan-features {
      padding: 0 35px 35px;
    }

    .plan-features ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .plan-features li {
      padding: 15px 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      font-size: 1rem;
      color: #495057;
      transition: all 0.2s ease;
    }

    .plan-features li:last-child {
      border-bottom: none;
    }

    .plan-features li::before {
      content: '✓';
      color: #28a745;
      font-weight: 900;
      margin-right: 15px;
      font-size: 1.2rem;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: linear-gradient(135deg, #d4edda, #c3e6cb);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .plan-features li:hover {
      color: #2c3e50;
      transform: translateX(5px);
    }

    .plan-footer {
      padding: 0 35px 45px;
    }

    .plan-button {
      width: 100%;
      padding: 18px 30px;
      border: none;
      border-radius: 16px;
      font-size: 1.1rem;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 1px;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      text-decoration: none;
      display: inline-block;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .plan-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s ease;
    }

    .plan-button:hover::before {
      left: 100%;
    }

    .plan-button.primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .plan-button.primary:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    .plan-button.secondary {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      color: #495057;
      border: 2px solid #dee2e6;
    }

    .plan-button.secondary:hover {
      background: linear-gradient(135deg, #e9ecef, #dee2e6);
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }

    .plan-button.current {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      cursor: default;
      box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    }



    .quota-info {
      background: linear-gradient(145deg, #f8f9fa, #ffffff);
      padding: 30px;
      border-radius: 20px;
      margin: 40px 0;
      text-align: center;
      box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .quota-info h3 {
      color: #2c3e50;
      margin-bottom: 25px;
      font-size: 1.5rem;
      font-weight: 700;
      background: linear-gradient(135deg, #2c3e50, #34495e);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .quota-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      font-size: 1.1rem;
      transition: all 0.2s ease;
    }

    .quota-item:last-child {
      border-bottom: none;
    }

    .quota-item:hover {
      color: #667eea;
      transform: translateX(5px);
    }

    .quota-item span:first-child {
      font-weight: 600;
      color: #495057;
    }

    .quota-item span:last-child {
      font-weight: 700;
      color: #2c3e50;
      background: linear-gradient(135deg, #e9ecef, #f8f9fa);
      padding: 8px 15px;
      border-radius: 12px;
      border: 1px solid rgba(0, 0, 0, 0.05);
    }

    @media (max-width: 1200px) {
      .plans-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 30px;
      }
    }

    @media (max-width: 768px) {
      .subscription-hero {
        padding: 80px 0;
      }

      .subscription-hero h1 {
        font-size: 2.8rem;
      }

      .subscription-hero p {
        font-size: 1.2rem;
        padding: 0 20px;
      }

      .plans-container {
        margin: -60px auto 80px;
        padding: 0 15px;
      }

      .plans-grid {
        grid-template-columns: 1fr;
        gap: 25px;
        margin-top: 40px;
      }

      .plan-card.current-plan,
      .plan-card.popular {
        transform: none;
      }

      .plan-card:hover {
        transform: translateY(-8px);
      }

      .plan-header {
        padding: 40px 25px 25px;
      }

      .plan-features {
        padding: 0 25px 25px;
      }

      .plan-footer {
        padding: 0 25px 35px;
      }

      .quota-info {
        padding: 25px 20px;
        margin: 30px 0;
      }
    }

    @media (max-width: 480px) {
      .subscription-hero h1 {
        font-size: 2.2rem;
      }

      .plan-name {
        font-size: 1.8rem;
      }

      .plan-price {
        font-size: 2.8rem;
      }

      .plan-card.popular::after {
        font-size: 0.65rem;
        padding: 8px 35px;
        right: -30px;
      }
    }
  </style>
</head>

<body>
  <!-- Navigation -->
  <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

  <!-- Hero Section -->
  <div class="subscription-hero">
    <div class="container">
      <h1>Choose Your Plan</h1>
      <p>Unlock the full potential of EVOLUTION888 with our flexible subscription plans designed for every need.</p>
    </div>
  </div>

  <!-- Plans Section -->
  <div class="plans-container">
    <?php if($currentSubscription): ?>
    <div class="quota-info">
      <h3>Your Current Usage</h3>
      <div class="quota-item">
        <span>Business Cards</span>
        <span><?php echo e(auth()->user()->businessCards()->count()); ?> / <?php echo e($currentSubscription->plan->max_business_cards == -1 ? '∞' : $currentSubscription->plan->max_business_cards); ?></span>
      </div>
      <div class="quota-item">
        <span>Club Profiles</span>
        <span><?php echo e(auth()->user()->clubProfile ? 1 : 0); ?> / <?php echo e($currentSubscription->plan->max_club_profiles == -1 ? '∞' : $currentSubscription->plan->max_club_profiles); ?></span>
      </div>
      <div class="quota-item">
        <span>Plan Status</span>
        <span><?php echo e($currentSubscription->status_display); ?></span>
      </div>
    </div>
    <?php endif; ?>

    <div class="plans-grid">
      <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <div class="plan-card <?php echo e($currentSubscription && $currentSubscription->plan_id == $plan->_id ? 'current-plan' : ''); ?> <?php echo e($index == 2 ? 'popular' : ''); ?>">
        <?php if($currentSubscription && $currentSubscription->plan_id == $plan->_id): ?>
        <div class="current-plan-badge">Current Plan</div>
        <?php endif; ?>

        <div class="plan-header">
          <div class="plan-icon" style="background-color: <?php echo e($plan->color); ?>">
            <i class="fas fa-<?php echo e($plan->icon); ?>"></i>
          </div>
          <h3 class="plan-name"><?php echo e($plan->display_name); ?></h3>
          <p class="plan-description"><?php echo e($plan->description); ?></p>
          <div class="plan-price <?php echo e($plan->monthly_price >= 100 ? 'large-price' : ''); ?>">
            <span class="currency">$</span><?php echo e(number_format($plan->monthly_price, 0)); ?>

            <span class="period">/month</span>
          </div>

        </div>

        <div class="plan-features">
          <ul>
            <?php $__currentLoopData = $plan->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li><?php echo e($feature); ?></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </ul>
        </div>

        <div class="plan-footer">
          <?php if($currentSubscription && $currentSubscription->plan_id == $plan->_id): ?>
          <button class="plan-button current">Current Plan</button>
          <?php else: ?>
          <button class="plan-button primary" onclick="purchasePlan('<?php echo e($plan->_id); ?>', '<?php echo e($plan->display_name); ?>', <?php echo e($plan->monthly_price); ?>)">
            Choose <?php echo e($plan->display_name); ?>

          </button>
          <?php endif; ?>
        </div>
      </div>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
  </div>

  <!-- Footer -->
  <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

  <script>

    function purchasePlan(planId, planName, price) {
      Swal.fire({
        title: `Purchase ${planName}?`,
        html: `
          <div style="text-align: left; margin: 20px 0;">
            <p><strong>Plan:</strong> ${planName}</p>
            <p><strong>Price:</strong> $${price}/month</p>
            <p><strong>Your Balance:</strong> $<?php echo e(number_format(auth()->user()->balance, 2)); ?></p>
            <hr style="margin: 15px 0;">
            <label style="display: flex; align-items: center; margin-top: 15px;">
              <input type="checkbox" id="autoRenew" checked style="margin-right: 10px;">
              Auto-renew monthly
            </label>
          </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#667eea',
        cancelButtonColor: '#95a5a6',
        confirmButtonText: 'Purchase Now',
        cancelButtonText: 'Cancel',
        preConfirm: () => {
          const autoRenew = document.getElementById('autoRenew').checked;
          return { auto_renew: autoRenew };
        }
      }).then((result) => {
        if (result.isConfirmed) {
          const loadingSwal = Swal.fire({
            title: 'Processing...',
            text: 'Please wait while we process your subscription.',
            allowOutsideClick: false,
            didOpen: () => {
              Swal.showLoading();
            }
          });

          $.ajax({
            url: `/subscriptions/purchase/${planId}`,
            method: 'POST',
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
              payment_method: 'balance',
              auto_renew: result.value.auto_renew
            },
            success: function(response) {
              loadingSwal.close();
              Swal.fire({
                title: 'Success!',
                text: response.message,
                icon: 'success',
                confirmButtonColor: '#667eea'
              }).then(() => {
                window.location.reload();
              });
            },
            error: function(xhr) {
              loadingSwal.close();
              const response = xhr.responseJSON;
              Swal.fire({
                title: 'Error!',
                text: response.message || 'Failed to purchase subscription',
                icon: 'error',
                confirmButtonColor: '#e74c3c'
              });
            }
          });
        }
      });
    }

    // Show upgrade modal if needed
    <?php if(session('show_upgrade_modal')): ?>
    $(document).ready(function() {
      Swal.fire({
        title: 'Subscription Required',
        text: '<?php echo e(session('error')); ?>',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#667eea',
        cancelButtonColor: '#95a5a6',
        confirmButtonText: 'View Plans',
        cancelButtonText: 'Cancel'
      });
    });
    <?php endif; ?>
  </script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/subscriptions/plans.blade.php ENDPATH**/ ?>