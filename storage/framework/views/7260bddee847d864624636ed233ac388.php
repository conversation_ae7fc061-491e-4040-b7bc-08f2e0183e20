<div class="sidebar-menu">
    <div class="sidebar-header">
        <div class="logo">
            <a href="/admin/">
                <h2 class="text-white">Admin</h2> 
            </a>
        </div>
    </div>
    <div class="main-menu">
        <div class="menu-inner">
            <nav>
                <ul class="metismenu" id="menu">
                    <li class="active">
                        <a href="javascript:void(0)" aria-expanded="true"><i class="ti-dashboard"></i><span>dashboard</span></a>
                        <ul class="collapse">
                            <li class="<?php echo e(Route::is('admin.index') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                        </ul>
                    </li>
                    <?php if(auth()->user()->can('admin.role.create') || auth()->user()->can('admin.role.view') ||  auth()->user()->can('admin.role.edit') ||  auth()->user()->can('admin.role.delete')): ?>
                    <li>
                        <a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-tasks"></i><span>
                            role & Permissions
                        </span></a>
                        <ul class="collapse <?php echo e(Route::is('admin.role.create') || Route::is('admin.role.index') || Route::is('admin.role.edit') || Route::is('admin.role.show') ? 'in' : ''); ?>">
                            <?php if(auth()->user()->can('admin.role.view')): ?>
                                <li class="<?php echo e(Route::is('admin.role.index')  || Route::is('admin.role.edit') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.role.index')); ?>">All role</a></li>
                            <?php endif; ?>
                            <?php if(auth()->user()->can('admin.role.create')): ?>
                                <li class="<?php echo e(Route::is('admin.role.create')  ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.role.create')); ?>">Create Role</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                    <?php endif; ?>
                    <?php if(auth()->user()->can('admin.users.view') ||  auth()->user()->can('admin.users.edit') ||  auth()->user()->can('admin.users.delete')): ?>
                    <li>
                        <a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-user"></i><span>
                            Users
                        </span></a>
                        <ul class="collapse <?php echo e(Route::is('admin.users.create') || Route::is('admin.users.index') || Route::is('admin.users.edit') ? 'in' : ''); ?>">
                            
                            <?php if(auth()->user()->can('admin.users.view')): ?>
                                <li class="<?php echo e(Route::is('admin.users.index')  || Route::is('admin.users.edit') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.users.index')); ?>">All Users</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                    <?php endif; ?>

                    
                    <?php if(auth()->user()->can('admin.business-cards.view') || auth()->user()->can('admin.business-cards.moderate')): ?>
                    <li>
                        <a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-id-card"></i><span>
                            Business Cards
                        </span></a>
                        <ul class="collapse <?php echo e(Route::is('admin.business-cards.*') ? 'in' : ''); ?>">
                            <?php if(auth()->user()->can('admin.business-cards.view')): ?>
                                <li class="<?php echo e(Route::is('admin.business-cards.index') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.business-cards.index')); ?>">All Cards</a>
                                </li>
                            <?php endif; ?>
                            <?php if(auth()->user()->can('admin.business-cards.moderate')): ?>
                                <li class="<?php echo e(Route::is('admin.business-cards.pending') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.business-cards.pending')); ?>">Pending Moderation</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </li>
                    <?php endif; ?>

                    
                    <?php if(auth()->user()->can('admin.club-profiles.view') || auth()->user()->can('admin.club-profiles.moderate')): ?>
                    <li>
                        <a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-users"></i><span>
                            Club Profiles
                        </span></a>
                        <ul class="collapse <?php echo e(Route::is('admin.club-profiles.*') ? 'in' : ''); ?>">
                            <?php if(auth()->user()->can('admin.club-profiles.view')): ?>
                                <li class="<?php echo e(Route::is('admin.club-profiles.index') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.club-profiles.index')); ?>">All Profiles</a>
                                </li>
                            <?php endif; ?>
                            <?php if(auth()->user()->can('admin.club-profiles.moderate')): ?>
                                <li class="<?php echo e(Route::is('admin.club-profiles.pending') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.club-profiles.pending')); ?>">Pending Moderation</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </li>
                    <?php endif; ?>

                    
                    <li>
                        <a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-crown"></i><span>
                            Subscription Plans
                        </span></a>
                        <ul class="collapse <?php echo e(Route::is('admin.subscription-plans.*') ? 'in' : ''); ?>">
                            <li class="<?php echo e(Route::is('admin.subscription-plans.index') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('admin.subscription-plans.index')); ?>">All Plans</a>
                            </li>
                            <li class="<?php echo e(Route::is('admin.subscription-plans.create') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('admin.subscription-plans.create')); ?>">Create Plan</a>
                            </li>
                        </ul>
                    </li>

                    <?php if(auth()->user()->can('admin.categories.view') ||  auth()->user()->can('admin.categories.edit') ||  auth()->user()->can('admin.categories.delete')): ?>
                    <li>
                        <a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-user"></i><span>
                            Categories 
                        </span></a>
                        <ul class="collapse <?php echo e(Route::is('admin.categories.create') || Route::is('admin.categories.index') || Route::is('admin.categories.edit') ? 'in' : ''); ?>">
                            
                            <?php if(auth()->user()->can('admin.categories.view')): ?>
                                <li class="<?php echo e(Route::is('admin.categories.index')  || Route::is('admin.categories.edit') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.categories.index')); ?>">All Categories</a></li>
                            <?php endif; ?>
                        </ul>
						<ul class="collapse <?php echo e(Route::is('admin.categories.create') || Route::is('admin.categories.index') || Route::is('admin.categories.edit') ? 'in' : ''); ?>">
                            <?php if(auth()->user()->can('admin.categories.create')): ?>
                                <li class="<?php echo e(Route::is('admin.categories.create')  ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.categories.create')); ?>">Create Category</a></li>
                            <?php endif; ?>		
						</ul>							
                    </li>
                    <?php endif; ?>				
                    <?php if(auth()->user()->can('admin.levels.view') ||  auth()->user()->can('admin.levels.edit') ||  auth()->user()->can('admin.levels.delete')): ?>
                    <li>
                        <a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-user"></i><span>
                            Levels 
                        </span></a>
                        <ul class="collapse <?php echo e(Route::is('admin.levels.create') || Route::is('admin.levels.index') || Route::is('admin.levels.edit') ? 'in' : ''); ?>">
                            
                            <?php if(auth()->user()->can('admin.levels.view')): ?>
                                <li class="<?php echo e(Route::is('admin.levels.index')  || Route::is('admin.levels.edit') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.levels.index')); ?>">All levels</a></li>
                            <?php endif; ?>
                        </ul>
						<ul class="collapse <?php echo e(Route::is('admin.levels.create') || Route::is('admin.levels.index') || Route::is('admin.levels.edit') ? 'in' : ''); ?>">
                            <?php if(auth()->user()->can('admin.levels.create')): ?>
                                <li class="<?php echo e(Route::is('admin.levels.create')  ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.levels.create')); ?>">Create Levels</a></li>
                            <?php endif; ?>		
						</ul>							
                    </li>
                    <?php endif; ?>		
                    <?php if(auth()->user()->can('admin.products.create') || auth()->user()->can('admin.products.view') ||  auth()->user()->can('admin.products.edit') ||  auth()->user()->can('admin.products.delete')): ?>
                    <li>
                        <a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-user"></i><span>
                            Products 
                        </span></a>
                        <ul class="collapse <?php echo e(Route::is('admin.products.create') || Route::is('admin.products.index') || Route::is('admin.products.edit') ? 'in' : ''); ?>">
                            
                            <?php if(auth()->user()->can('admin.products.view')): ?>
                                <li class="<?php echo e(Route::is('admin.products.index')  || Route::is('admin.products.edit') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.products.index')); ?>">All Products Card</a></li>
                            <?php endif; ?>
                        </ul>
						<ul class="collapse <?php echo e(Route::is('admin.products.create') || Route::is('admin.products.index') || Route::is('admin.products.edit') ? 'in' : ''); ?>">
                            <?php if(auth()->user()->can('admin.products.create')): ?>
                                <li class="<?php echo e(Route::is('admin.products.create')  ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.products.create')); ?>">Create Product Card</a></li>
                            <?php endif; ?>		
						</ul>	
                        <ul class="collapse <?php echo e(Route::is('admin.products_storage.create') || Route::is('admin.products_storage.index') || Route::is('admin.products_storage.edit') ? 'in' : ''); ?>">
                            
                            <?php if(auth()->user()->can('admin.products_storage.view')): ?>
                                <li class="<?php echo e(Route::is('admin.products_storage.index')  || Route::is('admin.products_storage.edit') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.products_storage.index')); ?>">All Products Storage</a></li>
                            <?php endif; ?>
                        </ul>
						<ul class="collapse <?php echo e(Route::is('admin.products_storage.create') || Route::is('admin.products_storage.index') || Route::is('admin.products_storage.edit') ? 'in' : ''); ?>">
                            <?php if(auth()->user()->can('admin.products_storage.create')): ?>
                                <li class="<?php echo e(Route::is('admin.products_storage.create')  ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.products_storage.create')); ?>">Create Product Storage</a></li>
                            <?php endif; ?>		
						</ul>							
                    </li>
                    <?php endif; ?>	
                    <?php if(auth()->user()->can('admin.codes.view') ||  auth()->user()->can('admin.codes.edit') ||  auth()->user()->can('admin.codes.delete')): ?>
                    <li>
                        <a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-user"></i><span>
                            Codes 
                        </span></a>
                        <ul class="collapse <?php echo e(Route::is('admin.codes.create') || Route::is('admin.codes.index') || Route::is('admin.codes.edit') ? 'in' : ''); ?>">
                            
                            <?php if(auth()->user()->can('admin.codes.view')): ?>
                                <li class="<?php echo e(Route::is('admin.codes.index')  || Route::is('admin.codes.edit') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.codes.index')); ?>">All Codes</a></li>
                            <?php endif; ?>
                        </ul>
						<ul class="collapse <?php echo e(Route::is('admin.codes.create') || Route::is('admin.codes.index') || Route::is('admin.codes.edit') ? 'in' : ''); ?>">
                            <?php if(auth()->user()->can('admin.codes.create')): ?>
                                <li class="<?php echo e(Route::is('admin.codes.create')  ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.codes.create')); ?>">Create Code</a></li>
                            <?php endif; ?>		
						</ul>							
                    </li>
                    <?php endif; ?>			
                    <?php if(auth()->user()->can('admin.partners.view') ||  auth()->user()->can('admin.partners.edit') ||  auth()->user()->can('admin.partners.delete')): ?>
                    <li>
                        <a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-user"></i><span>
                            Partners 
                        </span></a>
                        <ul class="collapse <?php echo e(Route::is('admin.partners.create') || Route::is('admin.partners.index') || Route::is('admin.partners.edit') ? 'in' : ''); ?>">
                            
                            <?php if(auth()->user()->can('admin.partners.view')): ?>
                                <li class="<?php echo e(Route::is('admin.partners.index')  || Route::is('admin.partners.edit') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.partners.index')); ?>">All Partners</a></li>
                            <?php endif; ?>
                        </ul>
						<ul class="collapse <?php echo e(Route::is('admin.partners.create') || Route::is('admin.partners.index') || Route::is('admin.partners.edit') ? 'in' : ''); ?>">
                            <?php if(auth()->user()->can('admin.partners.create')): ?>
                                <li class="<?php echo e(Route::is('admin.partners.create')  ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.partners.create')); ?>">Create Partner</a></li>
                            <?php endif; ?>		
						</ul>							
                    </li>
                    <?php endif; ?>	
                    <?php if(auth()->user()->can('admin.news.view') || auth()->user()->can('admin.news.create') || auth()->user()->can('admin.news.edit') ||  auth()->user()->can('admin.news.delete')): ?>
						<li>
							<a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-bullhorn"></i><span>News</span></a>
							<ul class="collapse <?php echo e(Route::is('admin.news.*') ? 'in' : ''); ?>">
								<?php if(auth()->user()->can('admin.news.view')): ?>
									<li class="<?php echo e(Route::is('admin.news.index') || Route::is('admin.news.edit') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.news.index')); ?>">All News</a></li>
								<?php endif; ?>
								<?php if(auth()->user()->can('admin.news.create')): ?>
									<li class="<?php echo e(Route::is('admin.news.create') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.news.create')); ?>">Create News</a></li>
								<?php endif; ?>
							</ul>
						</li>
                    <?php endif; ?>	
                    <?php if(auth()->user()->can('admin.projects.view') || auth()->user()->can('admin.projects.create') || auth()->user()->can('admin.projects.edit') ||  auth()->user()->can('admin.projects.delete')): ?>
						<li>
							<a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-bullhorn"></i><span>Projects</span></a>
							<ul class="collapse <?php echo e(Route::is('admin.projects.*') ? 'in' : ''); ?>">
								<?php if(auth()->user()->can('admin.projects.view')): ?>
									<li class="<?php echo e(Route::is('admin.projects.index') || Route::is('admin.projects.edit') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.projects.index')); ?>">All Projects</a></li>
								<?php endif; ?>
								<?php if(auth()->user()->can('admin.projects.create')): ?>
									<li class="<?php echo e(Route::is('admin.projects.create') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.projects.create')); ?>">Create Project</a></li>
								<?php endif; ?>
							</ul>
						</li>
                    <?php endif; ?>	
                    <?php if(auth()->user()->can('admin.social_projects.view') || auth()->user()->can('admin.social_projects.create') || auth()->user()->can('admin.social_projects.edit') ||  auth()->user()->can('admin.social_projects.delete')): ?>
						<li>
							<a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-bullhorn"></i><span>Social Projects</span></a>
							<ul class="collapse <?php echo e(Route::is('admin.social_projects.*') ? 'in' : ''); ?>">
								<?php if(auth()->user()->can('admin.social_projects.view')): ?>
									<li class="<?php echo e(Route::is('admin.social_projects.index') || Route::is('admin.social_projects.edit') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.social_projects.index')); ?>">All Social Projects</a></li>
								<?php endif; ?>
								<?php if(auth()->user()->can('admin.social_projects.create')): ?>
									<li class="<?php echo e(Route::is('admin.social_projects.create') ? 'active' : ''); ?>"><a href="<?php echo e(route('admin.social_projects.create')); ?>">Create Social Project</a></li>
								<?php endif; ?>
							</ul>
						</li>
                    <?php endif; ?>						
					<?php if(auth()->user()->can('admin.kyc.view') || auth()->user()->can('admin.kyc.approve') || auth()->user()->can('admin.kyc.decline')): ?>
					<li>
						<a href="javascript:void(0)" aria-expanded="true">
							<i class="fa fa-check-circle"></i><span>KYC</span>
						</a>
						<ul class="collapse <?php echo e(Route::is('admin.kyc.*') ? 'in' : ''); ?>">
							<?php if(auth()->user()->can('admin.kyc.view')): ?>
								<li class="<?php echo e(Route::is('admin.kyc.index') || Route::is('admin.kyc.show') ? 'active' : ''); ?>">
									<a href="<?php echo e(route('admin.kyc.index')); ?>">All KYC</a>
								</li>
							<?php endif; ?>
						</ul>
					</li>
					<?php endif; ?>	
					<?php if(auth()->user()->can('admin.payment_methods.view') || auth()->user()->can('admin.payment_methods.create') || auth()->user()->can('admin.payment_methods.edit') || auth()->user()->can('admin.payment_methods.delete')): ?>
						<li>
							<a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-credit-card"></i><span>Payment Methods</span></a>
							<ul class="collapse <?php echo e(Route::is('admin.payment_methods.*') ? 'in' : ''); ?>">
								<?php if(auth()->user()->can('admin.payment_methods.view')): ?>
									<li class="<?php echo e(Route::is('admin.payment_methods.index') || Route::is('admin.payment_methods.edit') ? 'active' : ''); ?>">
										<a href="<?php echo e(route('admin.payment_methods.index')); ?>">All Payment Methods</a>
									</li>
								<?php endif; ?>
								<?php if(auth()->user()->can('admin.payment_methods.create')): ?>
									<li class="<?php echo e(Route::is('admin.payment_methods.create') ? 'active' : ''); ?>">
										<a href="<?php echo e(route('admin.payment_methods.create')); ?>">Create Payment Method</a>
									</li>
								<?php endif; ?>
							</ul>
						</li>
					<?php endif; ?>	
					<?php if(auth()->user()->can('admin.withdrawals.view') || auth()->user()->can('admin.withdrawals.approve') || auth()->user()->can('admin.withdrawals.decline') || auth()->user()->can('admin.withdrawals.delete')): ?>
						<li>
							<a href="javascript:void(0)" aria-expanded="true"><i class="fa fa-money"></i><span>Withdrawal Requests</span></a>
							<ul class="collapse <?php echo e(Route::is('admin.withdrawals.*') ? 'in' : ''); ?>">
								<?php if(auth()->user()->can('admin.withdrawals.view')): ?>
									<li class="<?php echo e(Route::is('admin.withdrawals.index') || Route::is('admin.withdrawals.edit') ? 'active' : ''); ?>">
										<a href="<?php echo e(route('admin.withdrawals.index')); ?>">All Withdrawal Requests</a>
									</li>
								<?php endif; ?>
								<?php if(auth()->user()->can('admin.withdrawals.create')): ?>
									<li class="<?php echo e(Route::is('admin.withdrawals.create') ? 'active' : ''); ?>">
										<a href="<?php echo e(route('admin.withdrawals.create')); ?>">Create Withdrawal Request</a>
									</li>
								<?php endif; ?>
							</ul>
						</li>
					<?php endif; ?>					
                </ul>
            </nav>
        </div>
    </div>
</div>
<?php /**PATH /var/www/club/resources/views/admin/dashboard/layouts/partials/sidebar.blade.php ENDPATH**/ ?>