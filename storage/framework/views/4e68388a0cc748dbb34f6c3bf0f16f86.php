<?php $__env->startSection('title'); ?>
Dashboard Page - Admin Panel
<?php $__env->stopSection(); ?>


<?php $__env->startSection('admin-content'); ?>

<!-- page title area start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Dashboard</h4>
                <ul class="breadcrumbs pull-left">
                    <li></li>
                    <li><span>Dashboard</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- page title area end -->

<div class="main-content-inner">
  <div class="row">
    <div class="col-lg-8">
        <div class="row">
            <div class="col-md-6 mt-5 mb-3">
                <div class="card">
                    <div class="seo-fact sbg1">
						<div class="p-4 d-flex justify-content-between align-items-center">
							<div class="seofct-icon"><i class="fa fa-users"></i> Roles</div>
							<h2><?php echo e($total_roles); ?></h2>
						</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mt-md-5 mb-3">
                <div class="card">
                    <div class="seo-fact sbg2">
						<div class="p-4 d-flex justify-content-between align-items-center">
							<div class="seofct-icon"><i class="fa fa-user"></i> Users</div>
							<h2><?php echo e($total_users); ?></h2>
						</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3 mb-lg-0">
                <div class="card">
                    <div class="seo-fact sbg3">
                        <div class="p-4 d-flex justify-content-between align-items-center">
                            <div class="seofct-icon">Permissions</div>
                            <h2><?php echo e($total_permissions); ?></h2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3 mb-lg-0">
                <div class="card">
                    <div class="seo-fact sbg3">
                        <div class="p-4 d-flex justify-content-between align-items-center">
                            <div class="seofct-icon">Sum of activated code's</div>
                            <h2><?php echo e($redeemSum); ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Business Cards & Club Profiles Statistics -->
    <div class="col-lg-4">
        <div class="row">
            <div class="col-12 mt-5 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title mb-3">Business Cards</h4>
                        <div class="row">
                            <div class="col-6">
                                <div class="seo-fact sbg1">
                                    <div class="p-3 text-center">
                                        <div class="seofct-icon"><i class="fa fa-id-card"></i></div>
                                        <h3><?php echo e($total_business_cards); ?></h3>
                                        <p class="mb-0">Total Cards</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="seo-fact sbg2">
                                    <div class="p-3 text-center">
                                        <div class="seofct-icon"><i class="fa fa-check"></i></div>
                                        <h3><?php echo e($active_business_cards); ?></h3>
                                        <p class="mb-0">Active</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php if($pending_business_cards > 0): ?>
                        <div class="mt-3">
                            <a href="<?php echo e(route('admin.business-cards.pending')); ?>" class="btn btn-warning btn-block">
                                <i class="fa fa-clock-o"></i> <?php echo e($pending_business_cards); ?> Pending Moderation
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-12 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title mb-3">Club Profiles</h4>
                        <div class="row">
                            <div class="col-6">
                                <div class="seo-fact sbg3">
                                    <div class="p-3 text-center">
                                        <div class="seofct-icon"><i class="fa fa-users"></i></div>
                                        <h3><?php echo e($total_club_profiles); ?></h3>
                                        <p class="mb-0">Total Profiles</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="seo-fact sbg4">
                                    <div class="p-3 text-center">
                                        <div class="seofct-icon"><i class="fa fa-check"></i></div>
                                        <h3><?php echo e($active_club_profiles); ?></h3>
                                        <p class="mb-0">Active</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php if($pending_club_profiles > 0): ?>
                        <div class="mt-3">
                            <a href="<?php echo e(route('admin.club-profiles.pending')); ?>" class="btn btn-warning btn-block">
                                <i class="fa fa-clock-o"></i> <?php echo e($pending_club_profiles); ?> Pending Moderation
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/dashboard/index.blade.php ENDPATH**/ ?>