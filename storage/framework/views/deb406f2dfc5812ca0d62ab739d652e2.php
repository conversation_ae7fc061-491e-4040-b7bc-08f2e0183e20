<!DOCTYPE html>
<html data-wf-page="66b2a4c4c5b8b8b2e4b8b8b2" data-wf-site="66b2a4c4c5b8b8b2e4b8b8b1">

<head>
  <meta charset="utf-8">
  <title>Business Cards - Dashboard</title>
  <meta content="Manage your business cards" name="description">
  <meta content="Business Cards - Dashboard" property="og:title">
  <meta content="Manage your business cards" property="og:description">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="/css/normalize.css" rel="stylesheet" type="text/css">
  <link href="/css/webflow.css" rel="stylesheet" type="text/css">
  <link href="/css/uniontag.webflow.css" rel="stylesheet" type="text/css">
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin="anonymous">
  <script src="https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js" type="text/javascript"></script>
  <script type="text/javascript">WebFont.load({ google: { families: ["Inter:100,200,300,regular,500,600,700,800,900"] } });</script>
  <script type="text/javascript">!function (o, c) { var n = c.documentElement, t = " w-mod-"; n.className += t + "js", ("ontouchstart" in o || o.DocumentTouch && c instanceof DocumentTouch) && (n.className += t + "touch") }(window, document);</script>
  <link href="/images/favicon.ico" rel="shortcut icon" type="image/x-icon">
  <link href="/images/webclip.png" rel="apple-touch-icon">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    .business-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      border: 1px solid #e0e0e0;
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .business-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    }
    .card-logo {
      width: 50px;
      height: 50px;
      border-radius: 8px;
      object-fit: cover;
    }
    .card-stats {
      display: flex;
      gap: 15px;
      margin-top: 15px;
    }
    .stat-item {
      text-align: center;
      flex: 1;
    }
    .stat-number {
      font-size: 18px;
      font-weight: bold;
      color: #007bff;
    }
    .stat-label {
      font-size: 12px;
      color: #6c757d;
      margin-top: 4px;
    }
    .status-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
    .status-active { background: #d4edda; color: #155724; }
    .status-inactive { background: #f8d7da; color: #721c24; }
    .status-pending { background: #fff3cd; color: #856404; }
    .status-approved { background: #d1ecf1; color: #0c5460; }
    .status-rejected { background: #f8d7da; color: #721c24; }
  </style>
</head>

<body>
  <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

  <div class="page-wrapper">
    <section class="section template-pages-hero" style="margin-top: -120px;">
      <div class="w-layout-blockcontainer container-default w-container">
        <div class="card template-hero-card">
          <div class="inner-container _640px center">
            <div class="text-center">
              <h1 class="display-9 mid">My Business Cards</h1>
              <div class="mg-top-extra-small">
                <p class="paragraph-large">Manage your digital business cards</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="section">
      <div class="w-layout-blockcontainer container-default w-container">
        <div class="w-layout-grid grid-1-column">
          
          <!-- Action Buttons -->
          <div class="card feature-card-v1" style="padding: 20px; margin-bottom: 30px;">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
              <div>
                <h3 class="display-6 mid" style="margin: 0;">Business Cards Management</h3>
                <p style="margin: 5px 0 0 0; color: #6c757d;">Create and manage your digital business cards</p>
              </div>
              <div style="display: flex; gap: 10px;">
                <a href="/business-cards/create" class="primary-button dark-mode w-inline-block">
                  <div class="text-block">+ Create New Card</div>
                </a>
                <button onclick="refreshCards()" class="primary-button w-inline-block" style="background: #6c757d;">
                  <div class="text-block">🔄 Refresh</div>
                </button>
              </div>
            </div>
          </div>

          <!-- Business Cards Grid -->
          <div id="businessCardsContainer">
            <div style="text-align: center; padding: 50px;">
              <div class="spinner-border" role="status" style="width: 3rem; height: 3rem;">
                <span class="sr-only">Loading...</span>
              </div>
              <p style="margin-top: 15px;">Loading your business cards...</p>
            </div>
          </div>

        </div>
      </div>
    </section>
  </div>

  <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

  <script src="/js/webflow.js" type="text/javascript"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  
  <script>
    let businessCards = [];

    // Load business cards on page load
    document.addEventListener('DOMContentLoaded', function() {
      loadBusinessCards();
    });

    async function loadBusinessCards() {
      try {
        const response = await fetch('/api/get_business_cards');
        if (!response.ok) throw new Error('Failed to fetch business cards');
        
        businessCards = await response.json();
        renderBusinessCards();
      } catch (error) {
        console.error('Error loading business cards:', error);
        document.getElementById('businessCardsContainer').innerHTML = `
          <div style="text-align: center; padding: 50px;">
            <p style="color: #dc3545;">Error loading business cards. Please try again.</p>
            <button onclick="loadBusinessCards()" class="primary-button">Retry</button>
          </div>
        `;
      }
    }

    function renderBusinessCards() {
      const container = document.getElementById('businessCardsContainer');
      
      if (businessCards.length === 0) {
        container.innerHTML = `
          <div class="card feature-card-v1" style="text-align: center; padding: 50px;">
            <h3>No Business Cards Yet</h3>
            <p style="color: #6c757d; margin: 15px 0;">Create your first digital business card to get started.</p>
            <a href="/business-cards/create" class="primary-button dark-mode w-inline-block">
              <div class="text-block">Create Your First Card</div>
            </a>
          </div>
        `;
        return;
      }

      const cardsHtml = businessCards.map(card => `
        <div class="business-card">
          <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px;">
            <div style="display: flex; align-items: center; gap: 15px;">
              ${card.logo ? 
                `<img src="${card.logo}" alt="Logo" class="card-logo">` : 
                `<div class="card-logo" style="background: #f8f9fa; display: flex; align-items: center; justify-content: center; color: #6c757d;">
                  <i class="fas fa-image"></i>
                </div>`
              }
              <div>
                <h3 style="margin: 0; font-size: 18px;">${card.title}</h3>
                <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 14px;">
                  /tag/${card.profile_name}
                </p>
              </div>
            </div>
            <div style="display: flex; gap: 8px; align-items: center;">
              <span class="status-badge status-${card.is_active ? 'active' : 'inactive'}">
                ${card.is_active ? 'Active' : 'Inactive'}
              </span>
              <span class="status-badge status-${card.moderation_status}">
                ${card.moderation_status.charAt(0).toUpperCase() + card.moderation_status.slice(1)}
              </span>
            </div>
          </div>
          
          <p style="color: #6c757d; font-size: 14px; margin-bottom: 15px; line-height: 1.4;">
            ${card.description ? card.description.substring(0, 100) + (card.description.length > 100 ? '...' : '') : 'No description'}
          </p>
          
          <div class="card-stats">
            <div class="stat-item">
              <div class="stat-number">${card.views || 0}</div>
              <div class="stat-label">Views</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">${card.contacts ? card.contacts.length : 0}</div>
              <div class="stat-label">Contacts</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">${new Date(card.created_at).toLocaleDateString()}</div>
              <div class="stat-label">Created</div>
            </div>
          </div>
          
          <div style="display: flex; gap: 10px; margin-top: 15px; flex-wrap: wrap;">
            <a href="/tag/${card.profile_name}" target="_blank" class="primary-button w-inline-block" style="flex: 1; min-width: 120px;">
              <div class="text-block">👁 View Public</div>
            </a>
            <a href="/business-cards/${card._id}/edit" class="primary-button dark-mode w-inline-block" style="flex: 1; min-width: 120px;">
              <div class="text-block">✏️ Edit</div>
            </a>
            <button onclick="toggleCardStatus('${card._id}', ${card.is_active})" class="primary-button w-inline-block" 
                    style="background: ${card.is_active ? '#dc3545' : '#28a745'}; flex: 1; min-width: 120px;">
              <div class="text-block">${card.is_active ? '⏸ Deactivate' : '▶️ Activate'}</div>
            </button>
          </div>
        </div>
      `).join('');

      container.innerHTML = `
        <div class="w-layout-grid" style="grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px;">
          ${cardsHtml}
        </div>
      `;
    }

    async function toggleCardStatus(cardId, currentStatus) {
      try {
        const response = await fetch(`/business-cards/${cardId}/toggle-active`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          }
        });

        const result = await response.json();
        
        if (result.status === 'success') {
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: result.message,
            timer: 2000,
            showConfirmButton: false
          });
          loadBusinessCards(); // Refresh the cards
        } else {
          throw new Error(result.message);
        }
      } catch (error) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: error.message || 'Failed to update card status'
        });
      }
    }

    function refreshCards() {
      loadBusinessCards();
    }
  </script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/dashboard/business-cards.blade.php ENDPATH**/ ?>