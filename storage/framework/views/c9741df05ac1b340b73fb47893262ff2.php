<!DOCTYPE html>
<html data-wf-domain="/" data-wf-page="663d095d8e07618c5381377b" data-wf-site="663b748d327c826a2952af46" data-wf-status="1" lang="en" class=" w-mod-js w-mod-ix">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><?php echo e($profile->user->full_name ?? 'Business Member'); ?> - EVOLUTION888 Club</title>

  <!-- Meta tags for social sharing -->
  <meta property="og:title" content="<?php echo e($profile->user->full_name ?? 'Business Member'); ?> - EVOLUTION888 Club">
  <meta property="og:description" content="<?php echo e(Str::limit($profile->description, 160)); ?>">
  <meta property="og:type" content="profile">
  <meta property="og:url" content="<?php echo e($profile->public_url); ?>">
  <?php if($profile->banner_image): ?>
    <meta property="og:image" content="<?php echo e($profile->banner_image); ?>">
  <?php endif; ?>

  <!-- Main CSS -->
  <link rel="stylesheet" href="<?php echo e(asset('css/style.css')); ?>" />
  <link href="css/techbetatemplates.webflow.14600f26e.css?v=0.3" rel="stylesheet" type="text/css">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8fafc;
      color: #1a202c;
      line-height: 1.6;
    }

    .profile-container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 24px;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
      margin-top: 40px;
      margin-bottom: 40px;
    }

    .profile-header {
      position: relative;
      height: 400px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
      overflow: hidden;
    }

    .profile-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
    }

    .profile-banner {
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0.8;
    }

    .profile-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.6));
    }

    .profile-info {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 40px;
      color: white;
      z-index: 10;
    }

    .profile-avatar {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      border: 6px solid rgba(255, 255, 255, 0.9);
      margin-bottom: 24px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 48px;
      color: white;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      backdrop-filter: blur(10px);
      position: relative;
      overflow: hidden;
    }

    .profile-avatar::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
      transform: rotate(45deg);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
      100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    .profile-name {
      font-size: 42px;
      font-weight: 800;
      margin-bottom: 12px;
      text-shadow: 0 4px 8px rgba(0,0,0,0.3);
      letter-spacing: -0.5px;
      background: linear-gradient(135deg, #ffffff, #f0f0f0);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .profile-url {
      font-size: 18px;
      opacity: 0.9;
      margin-bottom: 24px;
      font-family: 'JetBrains Mono', 'Courier New', monospace;
      background: rgba(255, 255, 255, 0.1);
      padding: 8px 16px;
      border-radius: 12px;
      display: inline-block;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .profile-stats {
      display: flex;
      gap: 32px;
      flex-wrap: wrap;
      margin-top: 20px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 16px;
      font-weight: 500;
      opacity: 0.95;
      background: rgba(255, 255, 255, 0.1);
      padding: 12px 18px;
      border-radius: 16px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
    }

    .stat-item:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }

    .stat-item i {
      font-size: 18px;
      opacity: 0.8;
    }
    .profile-content {
      padding: 60px 40px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }

    .content-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 40px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .main-content {
      display: flex;
      flex-direction: column;
      gap: 32px;
    }

    .sidebar-content {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .content-section {
      background: white;
      border-radius: 20px;
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
      padding: 40px;
      border: 1px solid rgba(226, 232, 240, 0.8);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .content-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .content-section:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
    }

    .section-title {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 24px;
      color: #1a202c;
      display: flex;
      align-items: center;
      gap: 12px;
      letter-spacing: -0.3px;
    }

    .section-title i {
      font-size: 20px;
      color: #667eea;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .description-text {
      color: #4a5568;
      line-height: 1.8;
      font-size: 18px;
      font-weight: 400;
      letter-spacing: 0.2px;
    }
    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }

    .tag {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 12px 20px;
      border-radius: 25px;
      font-size: 15px;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      position: relative;
      overflow: hidden;
    }

    .tag::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    .tag:hover::before {
      left: 100%;
    }

    .tag:hover {
      transform: translateY(-3px) scale(1.05);
      text-decoration: none;
      color: white;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
    .back-button {
      position: fixed;
      top: 30px;
      left: 30px;
      background: rgba(255, 255, 255, 0.95);
      color: #1a202c;
      padding: 16px 24px;
      border-radius: 50px;
      text-decoration: none;
      font-size: 16px;
      font-weight: 600;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      backdrop-filter: blur(20px);
      transition: all 0.3s ease;
      z-index: 1000;
      border: 1px solid rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .back-button:hover {
      background: white;
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
      text-decoration: none;
      color: #667eea;
    }

    .back-button i {
      transition: transform 0.3s ease;
    }

    .back-button:hover i {
      transform: translateX(-3px);
    }
    .membership-badge {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    .contact-section {
      text-align: center;
      padding: 32px;
      background: linear-gradient(135deg, #f8fafc, #e2e8f0);
      border-radius: 20px;
      margin-top: 24px;
      border: 1px solid rgba(226, 232, 240, 0.8);
    }

    .contact-section p {
      font-size: 16px;
      color: #4a5568;
      margin-bottom: 24px;
      line-height: 1.6;
    }

    .contact-button {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 16px 32px;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      font-size: 16px;
      display: inline-flex;
      align-items: center;
      gap: 12px;
      transition: all 0.3s ease;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
      position: relative;
      overflow: hidden;
    }

    .contact-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    .contact-button:hover::before {
      left: 100%;
    }

    .contact-button:hover {
      transform: translateY(-3px) scale(1.05);
      text-decoration: none;
      color: white;
      box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    }
    
    @media (max-width: 768px) {
      .profile-container {
        margin: 20px;
        border-radius: 16px;
      }

      .profile-header {
        height: 300px;
      }

      .profile-info {
        padding: 30px 20px;
      }

      .profile-name {
        font-size: 32px;
      }

      .profile-url {
        font-size: 16px;
        padding: 6px 12px;
      }

      .profile-stats {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
      }

      .stat-item {
        font-size: 14px;
        padding: 10px 14px;
      }

      .profile-content {
        padding: 40px 20px;
      }

      .content-grid {
        grid-template-columns: 1fr;
        gap: 24px;
      }

      .content-section {
        padding: 24px;
      }

      .section-title {
        font-size: 20px;
      }

      .description-text {
        font-size: 16px;
      }

      .back-button {
        position: static;
        margin: 20px;
        display: inline-flex;
        padding: 12px 20px;
        font-size: 14px;
      }

      .contact-section {
        padding: 24px;
      }

      .contact-button {
        padding: 14px 28px;
        font-size: 15px;
      }
    }
  </style>
</head>

<body>
  <!-- Back Button -->
  <a href="<?php echo e(route('club.members')); ?>" class="back-button">
    <i class="fas fa-arrow-left"></i> Back to Members
  </a>

  <div class="profile-container">
    <!-- Profile Header -->
    <div class="profile-header">
      <?php if($profile->banner_image): ?>
        <img src="<?php echo e($profile->banner_image); ?>" alt="Profile Banner" class="profile-banner">
      <?php endif; ?>
      <div class="profile-overlay"></div>

      <div class="profile-info">
        <div class="profile-avatar">
          <?php if($profile->user->avatar): ?>
            <img src="<?php echo e($profile->user->avatar); ?>" alt="<?php echo e($profile->user->full_name ?? 'Member'); ?>" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
          <?php else: ?>
            <i class="fas fa-building"></i>
          <?php endif; ?>
        </div>

        <h1 class="profile-name"><?php echo e($profile->user->full_name ?? 'Business Member'); ?></h1>
        <p class="profile-url">/profile/<?php echo e($profile->profile_name); ?></p>

        <!-- Membership Badge -->
        <?php if($profile->user->membership_badge): ?>
          <div style="margin-bottom: 20px;">
            <?php echo $profile->user->membership_badge; ?>

          </div>
        <?php endif; ?>

        <div class="profile-stats">
          <div class="stat-item">
            <i class="fas fa-eye"></i>
            <span><?php echo e(number_format($profile->views_count)); ?> views</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-calendar-alt"></i>
            <span>Member since <?php echo e($profile->created_at->format('M Y')); ?></span>
          </div>
          <?php if($profile->tags && count($profile->tags) > 0): ?>
            <div class="stat-item">
              <i class="fas fa-tags"></i>
              <span><?php echo e(count($profile->tags)); ?> expertise areas</span>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>

    <!-- Profile Content -->
    <div class="profile-content">
      <div class="content-grid">
        <div class="main-content">
          <!-- About Section -->
          <?php if($profile->description): ?>
            <div class="content-section">
              <h2 class="section-title">
                <i class="fas fa-user-tie"></i>
                About <?php echo e($profile->user->full_name ?? 'This Member'); ?>

              </h2>
              <div class="description-text"><?php echo e($profile->description); ?></div>
            </div>
          <?php endif; ?>

          <!-- Interests/Tags Section -->
          <?php if($profile->tags && count($profile->tags) > 0): ?>
            <div class="content-section">
              <h2 class="section-title">
                <i class="fas fa-lightbulb"></i>
                Expertise & Interests
              </h2>
              <div class="tags-container">
                <?php $__currentLoopData = $profile->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <a href="<?php echo e(route('club.members', ['tags' => $tag])); ?>" class="tag">
                    <?php echo e(ucfirst($tag)); ?>

                  </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </div>
            </div>
          <?php endif; ?>
        </div>

        <div class="sidebar-content">
          <!-- Stats Section -->
          <div class="content-section">
            <h2 class="section-title">
              <i class="fas fa-chart-line"></i>
              Profile Stats
            </h2>
            <div style="display: flex; flex-direction: column; gap: 16px;">
              <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #e2e8f0;">
                <span style="color: #4a5568; font-weight: 500;">Profile Views</span>
                <span style="color: #1a202c; font-weight: 700; font-size: 18px;"><?php echo e(number_format($profile->views_count)); ?></span>
              </div>
              <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #e2e8f0;">
                <span style="color: #4a5568; font-weight: 500;">Member Since</span>
                <span style="color: #1a202c; font-weight: 700;"><?php echo e($profile->created_at->format('M Y')); ?></span>
              </div>
              <?php if($profile->tags && count($profile->tags) > 0): ?>
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0;">
                  <span style="color: #4a5568; font-weight: 500;">Expertise Areas</span>
                  <span style="color: #1a202c; font-weight: 700; font-size: 18px;"><?php echo e(count($profile->tags)); ?></span>
                </div>
              <?php endif; ?>
            </div>
          </div>

          <!-- Contact Section -->
          <div class="content-section">
            <h2 class="section-title">
              <i class="fas fa-paper-plane"></i>
              Get In Touch
            </h2>
            <div class="contact-section">
              <?php if($canViewContacts): ?>
                <p>Ready to connect and explore business opportunities together?</p>
                <a href="mailto:<?php echo e($profile->user->email); ?>" class="contact-button">
                  <i class="fas fa-envelope"></i>
                  Send Message
                </a>
              <?php else: ?>
                <div style="text-align: center; padding: 30px; background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 16px; border: 2px dashed #dee2e6;">
                  <div style="font-size: 48px; color: #6c757d; margin-bottom: 20px;">
                    <i class="fas fa-lock"></i>
                  </div>
                  <h3 style="color: #495057; margin-bottom: 15px; font-size: 1.3rem;">Subscription Required</h3>
                  <p style="color: #6c757d; margin-bottom: 25px; line-height: 1.6;">
                    To view contact information and connect with business club members, you need an active subscription.
                  </p>
                  <?php if(auth()->guard()->check()): ?>
                    <a href="<?php echo e(route('subscriptions.plans')); ?>" style="display: inline-block; padding: 14px 28px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; text-decoration: none; border-radius: 12px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);">
                      <i class="fas fa-crown" style="margin-right: 8px;"></i>
                      Upgrade Now
                    </a>
                  <?php else: ?>
                    <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                      <a href="<?php echo e(route('login')); ?>" style="display: inline-block; padding: 14px 28px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; text-decoration: none; border-radius: 12px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);">
                        <i class="fas fa-sign-in-alt" style="margin-right: 8px;"></i>
                        Login
                      </a>
                      <a href="<?php echo e(route('login')); ?>" style="display: inline-block; padding: 14px 28px; background: linear-gradient(135deg, #28a745, #20c997); color: white; text-decoration: none; border-radius: 12px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);">
                        <i class="fas fa-user-plus" style="margin-right: 8px;"></i>
                        Sign Up
                      </a>
                    </div>
                  <?php endif; ?>
                </div>
              <?php endif; ?>
            </div>
          </div>
        </div>
      </div>
    </div>

    </div>
  </div>

  <script>
    // Add some interactive effects
    document.addEventListener('DOMContentLoaded', function() {
      // Parallax effect for header
      window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const header = document.querySelector('.profile-header');
        const banner = document.querySelector('.profile-banner');
        
        if (header && banner) {
          banner.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
      });

      // Animate content sections on scroll
      const sections = document.querySelectorAll('.content-section');
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
          }
        });
      }, { threshold: 0.1 });

      sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(section);
      });
    });
  </script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/club/profile.blade.php ENDPATH**/ ?>