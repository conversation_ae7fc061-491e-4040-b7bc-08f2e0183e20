<?php $__env->startSection('title'); ?>
Business Card Details - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <style>
        .card-logo {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
        }
        .contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .contact-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin-right: 15px;
            color: white;
        }
        .qr-code {
            max-width: 200px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .info-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .moderation-history {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<!-- Page Title Area Start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Business Card Details</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.business-cards.index')); ?>">Business Cards</a></li>
                    <li><span><?php echo e($card->title); ?></span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- Page Title Area End -->

<div class="main-content-inner">
    <div class="row">
        <!-- Card Preview -->
        <div class="col-lg-8 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-4">Business Card Preview</h4>
                    
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <?php if($card->logo): ?>
                                <img src="<?php echo e(Storage::url($card->logo)); ?>" alt="Logo" class="card-logo mb-3">
                            <?php else: ?>
                                <div class="card-logo bg-light d-flex align-items-center justify-content-center mb-3 mx-auto">
                                    <i class="fa fa-image text-muted fa-2x"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-9">
                            <h3 class="mb-2"><?php echo e($card->title); ?></h3>
                            <p class="text-muted mb-3">
                                <i class="fa fa-link"></i>
                                <a href="<?php echo e($card->public_url); ?>" target="_blank" class="text-primary">
                                    <?php echo e($card->public_url); ?>

                                </a>
                            </p>
                            
                            <?php if($card->description): ?>
                                <p class="mb-3"><?php echo e($card->description); ?></p>
                            <?php endif; ?>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Status:</strong> 
                                        <?php if($card->is_active): ?>
                                            <span class="badge badge-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </p>
                                    <p class="mb-1"><strong>Views:</strong> <?php echo e(number_format($card->views_count)); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Moderation:</strong> <?php echo $card->moderation_badge; ?></p>
                                    <p class="mb-1"><strong>Created:</strong> <?php echo e($card->created_at->format('M d, Y H:i')); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <?php if($card->contacts && count($card->contacts) > 0): ?>
                        <hr>
                        <h5 class="mb-3">Contact Information</h5>
                        <div class="row">
                            <?php $__currentLoopData = $card->formatted_contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-6 mb-3">
                                    <div class="contact-item">
                                        <div class="contact-icon" style="background-color: <?php echo e($contact['color']); ?>;">
                                            <i class="fa fa-<?php echo e($contact['icon']); ?>"></i>
                                        </div>
                                        <div>
                                            <strong><?php echo e($contact['label'] ?: ucfirst($contact['type'])); ?></strong><br>
                                            <span class="text-muted"><?php echo e($contact['value']); ?></span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($card->qr_code): ?>
                        <hr>
                        <h5 class="mb-3">QR Code</h5>
                        <img src="<?php echo e(Storage::url($card->qr_code)); ?>" alt="QR Code" class="qr-code">
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Card Info & Actions -->
        <div class="col-lg-4 mt-5">
            <!-- User Info -->
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">User Information</h4>
                    <?php if($card->user): ?>
                        <p><strong>Name:</strong> <?php echo e($card->user->name); ?></p>
                        <p><strong>Email:</strong> <?php echo e($card->user->email); ?></p>
                        <p><strong>Username:</strong> <?php echo e($card->user->username ?? 'N/A'); ?></p>
                        <p><strong>Joined:</strong> <?php echo e($card->user->created_at->format('M d, Y')); ?></p>
                    <?php else: ?>
                        <p class="text-muted">User information not available</p>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Moderation Info -->
            <?php if($card->moderated_at || $card->moderation_notes): ?>
            <div class="card mt-4">
                <div class="card-body">
                    <h4 class="header-title mb-3">Moderation History</h4>
                    
                    <?php if($card->moderated_at): ?>
                        <p><strong>Moderated:</strong> <?php echo e($card->moderated_at->format('M d, Y H:i')); ?></p>
                        <?php if($card->moderator): ?>
                            <p><strong>Moderator:</strong> <?php echo e($card->moderator->name); ?></p>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <?php if($card->moderation_notes): ?>
                        <div class="moderation-history">
                            <strong>Notes:</strong><br>
                            <?php echo e($card->moderation_notes); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Actions -->
            <div class="card mt-4">
                <div class="card-body">
                    <h4 class="header-title mb-3">Actions</h4>
                    
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.business-cards.edit', $card->_id)); ?>" class="btn btn-primary btn-block">
                            <i class="fa fa-edit"></i> Edit Card
                        </a>
                        
                        <?php if($card->isPending()): ?>
                            <button class="btn btn-success btn-block approve-btn" data-id="<?php echo e($card->_id); ?>">
                                <i class="fa fa-check"></i> Approve
                            </button>
                            <button class="btn btn-warning btn-block reject-btn" data-id="<?php echo e($card->_id); ?>">
                                <i class="fa fa-times"></i> Reject
                            </button>
                        <?php endif; ?>
                        
                        <button class="btn btn-info btn-block toggle-status-btn" data-id="<?php echo e($card->_id); ?>">
                            <i class="fa fa-toggle-<?php echo e($card->is_active ? 'on' : 'off'); ?>"></i>
                            <?php echo e($card->is_active ? 'Deactivate' : 'Activate'); ?>

                        </button>
                        
                        <a href="<?php echo e($card->public_url); ?>" target="_blank" class="btn btn-secondary btn-block">
                            <i class="fa fa-external-link"></i> View Public
                        </a>
                        
                        <button class="btn btn-danger btn-block delete-btn" data-id="<?php echo e($card->_id); ?>">
                            <i class="fa fa-trash"></i> Delete Card
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Moderation Modal -->
<div class="modal fade" id="moderationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Moderate Business Card</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="moderationForm">
                    <input type="hidden" id="cardId">
                    <input type="hidden" id="action">
                    <div class="form-group">
                        <label>Notes</label>
                        <textarea class="form-control" id="notes" rows="3" placeholder="Add moderation notes..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmModeration">Confirm</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Approve button
            $('.approve-btn').click(function() {
                const cardId = $(this).data('id');
                $('#cardId').val(cardId);
                $('#action').val('approve');
                $('.modal-title').text('Approve Business Card');
                $('#notes').attr('placeholder', 'Add approval notes (optional)...');
                $('#moderationModal').modal('show');
            });

            // Reject button
            $('.reject-btn').click(function() {
                const cardId = $(this).data('id');
                $('#cardId').val(cardId);
                $('#action').val('reject');
                $('.modal-title').text('Reject Business Card');
                $('#notes').attr('placeholder', 'Add rejection reason (required)...');
                $('#moderationModal').modal('show');
            });

            // Confirm moderation
            $('#confirmModeration').click(function() {
                const cardId = $('#cardId').val();
                const action = $('#action').val();
                const notes = $('#notes').val();
                
                if (action === 'reject' && !notes.trim()) {
                    alert('Rejection reason is required');
                    return;
                }
                
                $.post(`/admin/business-cards/${cardId}/${action}`, {
                    _token: '<?php echo e(csrf_token()); ?>',
                    notes: notes
                }).done(function(response) {
                    if (response.success) {
                        location.reload();
                    }
                });
            });

            // Toggle status
            $('.toggle-status-btn').click(function() {
                const cardId = $(this).data('id');
                
                $.post(`/admin/business-cards/${cardId}/toggle-active`, {
                    _token: '<?php echo e(csrf_token()); ?>'
                }).done(function(response) {
                    if (response.success) {
                        location.reload();
                    }
                });
            });

            // Delete button
            $('.delete-btn').click(function() {
                if (confirm('Are you sure you want to delete this business card? This action cannot be undone.')) {
                    const cardId = $(this).data('id');
                    const form = $('<form method="POST" action="/admin/business-cards/' + cardId + '">' +
                        '<input type="hidden" name="_token" value="<?php echo e(csrf_token()); ?>">' +
                        '<input type="hidden" name="_method" value="DELETE">' +
                        '</form>');
                    $('body').append(form);
                    form.submit();
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/business-cards/show.blade.php ENDPATH**/ ?>