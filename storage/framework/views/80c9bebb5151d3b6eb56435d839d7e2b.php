<!DOCTYPE html>
<html data-wf-domain="/" data-wf-page="663d095d8e07618c5381377b" data-wf-site="663b748d327c826a2952af46" data-wf-status="1" lang="en" class=" w-mod-js w-mod-ix">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>EVOLUTION888 business center - My Business Cards</title>

  <!-- CSRF Token (Laravel) -->
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <!-- Main CSS -->
  <link href="/css/techbetatemplates.webflow.14600f26e.css?v=0.3" rel="stylesheet" type="text/css">

  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
<div class="page-wrapper">

  <!-- ---------- Navbar ---------- -->
  <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

  <!-- ---------- Main Content ---------- -->
  <section class="section">
    <div class="w-layout-blockcontainer container-default w-container">
      <div class="w-layout-grid grid-1-column">
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">My Business Cards</h4>
                    <a href="<?php echo e(route('dashboard.business-cards.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create New Card
                    </a>
                </div>
                <div class="card-body">
                    <?php if($businessCards->isEmpty()): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-id-card fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Business Cards Yet</h5>
                            <p class="text-muted">Create your first business card to get started.</p>
                            <a href="<?php echo e(route('dashboard.business-cards.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Business Card
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php $__currentLoopData = $businessCards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100 shadow-sm">
                                        <?php if($card->logo): ?>
                                            <div class="card-img-top d-flex justify-content-center align-items-center" style="height: 120px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                                <img src="<?php echo e(asset('storage/' . $card->logo)); ?>" alt="Logo" class="img-fluid" style="max-height: 80px; max-width: 80px;">
                                            </div>
                                        <?php else: ?>
                                            <div class="card-img-top d-flex justify-content-center align-items-center" style="height: 120px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                                <i class="fas fa-id-card fa-3x text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="card-body">
                                            <h5 class="card-title"><?php echo e($card->title); ?></h5>
                                            <h6 class="card-subtitle mb-2 text-muted"><?php echo e($card->profile_name); ?></h6>
                                            <p class="card-text"><?php echo e($card->company); ?></p>
                                            <p class="card-text"><?php echo e($card->position); ?></p>
                                            
                                            <div class="mb-2">
                                                <?php if($card->is_active): ?>
                                                    <span class="status-badge status-active">✓ Active</span>
                                                <?php else: ?>
                                                    <span class="status-badge status-inactive">⚪ Inactive</span>
                                                <?php endif; ?>

                                                <?php if($card->moderation_status === 'pending'): ?>
                                                    <span class="status-badge status-pending">⏳ Pending Review</span>
                                                <?php elseif($card->moderation_status === 'approved'): ?>
                                                    <span class="status-badge status-approved">✅ Approved</span>
                                                <?php elseif($card->moderation_status === 'rejected'): ?>
                                                    <span class="status-badge status-rejected">❌ Rejected</span>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <small class="text-muted">
                                                <i class="fas fa-eye"></i> <?php echo e($card->views_count); ?> views
                                            </small>
                                        </div>
                                        
                                        <div class="card-footer bg-transparent">
                                            <div class="btn-group w-100" role="group">
                                                <a href="<?php echo e(url('/tag/' . $card->profile_name)); ?>"
                                                   class="btn btn-outline-primary btn-sm" target="_blank">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                <a href="<?php echo e(route('dashboard.business-cards.edit', $card)); ?>"
                                                   class="btn btn-outline-secondary btn-sm">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                                        onclick="deleteCard('<?php echo e($card->_id); ?>')">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Business Card</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this business card? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
function deleteCard(cardId) {
    const form = document.getElementById('deleteForm');
    form.action = `/dashboard/business-cards/${cardId}`;
    $('#deleteModal').modal('show');
}
</script>

<style>
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    margin-right: 8px;
    margin-bottom: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
}

.status-active {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.status-inactive {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.status-pending {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.status-approved {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.status-rejected {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}
</style>

</body>
</html>
<?php /**PATH /var/www/club/resources/views/dashboard/business-cards/index.blade.php ENDPATH**/ ?>