<?php $__env->startSection('title', 'Create Subscription Plan'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .form-group label {
        font-weight: 600;
        color: #2c3e50;
    }
    
    .feature-input, .permission-input {
        margin-bottom: 10px;
    }
    
    .remove-btn {
        background: #dc3545;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .add-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 10px;
    }
    
    .color-preview {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 2px solid #ddd;
        display: inline-block;
        margin-left: 10px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>
<!-- Page Title -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Create Subscription Plan</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.subscription-plans.index')); ?>">Subscription Plans</a></li>
                    <li><span>Create</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <div class="user-profile pull-right">
                <img class="avatar user-thumb" src="<?php echo e(asset('backend/assets/images/author/avatar.png')); ?>" alt="avatar">
                <h4 class="user-name dropdown-toggle" data-toggle="dropdown"><?php echo e(auth()->user()->name); ?> <i class="fa fa-angle-down"></i></h4>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="#">Message</a>
                    <a class="dropdown-item" href="#">Settings</a>
                    <a class="dropdown-item" href="#">Log Out</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<div class="main-content-inner">
    <div class="row">
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Create New Subscription Plan</h4>
                    
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <form action="<?php echo e(route('admin.subscription-plans.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Plan Name (Internal)</label>
                                    <input type="text" class="form-control" id="name" name="name" value="<?php echo e(old('name')); ?>" required>
                                    <small class="form-text text-muted">Used internally, no spaces or special characters</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="display_name">Display Name</label>
                                    <input type="text" class="form-control" id="display_name" name="display_name" value="<?php echo e(old('display_name')); ?>" required>
                                    <small class="form-text text-muted">Name shown to users</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" required><?php echo e(old('description')); ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="monthly_price">Monthly Price ($)</label>
                                    <input type="number" class="form-control" id="monthly_price" name="monthly_price" step="0.01" min="0" value="<?php echo e(old('monthly_price')); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="trial_days">Trial Days</label>
                                    <input type="number" class="form-control" id="trial_days" name="trial_days" min="0" max="365" value="<?php echo e(old('trial_days', 0)); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="sort_order">Sort Order</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" min="0" value="<?php echo e(old('sort_order', 0)); ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="max_business_cards">Max Business Cards</label>
                                    <input type="number" class="form-control" id="max_business_cards" name="max_business_cards" min="-1" value="<?php echo e(old('max_business_cards', 1)); ?>" required>
                                    <small class="form-text text-muted">Use -1 for unlimited</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="max_club_profiles">Max Club Profiles</label>
                                    <input type="number" class="form-control" id="max_club_profiles" name="max_club_profiles" min="-1" value="<?php echo e(old('max_club_profiles', 1)); ?>" required>
                                    <small class="form-text text-muted">Use -1 for unlimited</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="color">Color</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control" id="color" name="color" value="<?php echo e(old('color', '#007bff')); ?>" required style="width: 60px; height: 40px;">
                                        <div class="color-preview ml-2" id="colorPreview" style="background-color: <?php echo e(old('color', '#007bff')); ?>;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="icon">Icon (FontAwesome)</label>
                                    <input type="text" class="form-control" id="icon" name="icon" value="<?php echo e(old('icon', 'user')); ?>" required>
                                    <small class="form-text text-muted">FontAwesome icon name (without fa-)</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" name="is_active" value="1" <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                                        Active Plan
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Features</label>
                                    <div id="features-container">
                                        <?php if(old('features')): ?>
                                            <?php $__currentLoopData = old('features'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="feature-input d-flex">
                                                    <input type="text" class="form-control" name="features[]" value="<?php echo e($feature); ?>" required>
                                                    <button type="button" class="remove-btn ml-2" onclick="removeInput(this)">Remove</button>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <div class="feature-input d-flex">
                                                <input type="text" class="form-control" name="features[]" placeholder="Enter feature" required>
                                                <button type="button" class="remove-btn ml-2" onclick="removeInput(this)">Remove</button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <button type="button" class="add-btn" onclick="addFeature()">Add Feature</button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Permissions</label>
                                    <div id="permissions-container">
                                        <?php if(old('permissions')): ?>
                                            <?php $__currentLoopData = old('permissions'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="permission-input d-flex">
                                                    <input type="text" class="form-control" name="permissions[]" value="<?php echo e($permission); ?>" required>
                                                    <button type="button" class="remove-btn ml-2" onclick="removeInput(this)">Remove</button>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <div class="permission-input d-flex">
                                                <input type="text" class="form-control" name="permissions[]" placeholder="Enter permission" required>
                                                <button type="button" class="remove-btn ml-2" onclick="removeInput(this)">Remove</button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <button type="button" class="add-btn" onclick="addPermission()">Add Permission</button>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">Create Plan</button>
                            <a href="<?php echo e(route('admin.subscription-plans.index')); ?>" class="btn btn-secondary ml-2">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Color preview update
    document.getElementById('color').addEventListener('input', function() {
        document.getElementById('colorPreview').style.backgroundColor = this.value;
    });

    function addFeature() {
        const container = document.getElementById('features-container');
        const div = document.createElement('div');
        div.className = 'feature-input d-flex';
        div.innerHTML = `
            <input type="text" class="form-control" name="features[]" placeholder="Enter feature" required>
            <button type="button" class="remove-btn ml-2" onclick="removeInput(this)">Remove</button>
        `;
        container.appendChild(div);
    }

    function addPermission() {
        const container = document.getElementById('permissions-container');
        const div = document.createElement('div');
        div.className = 'permission-input d-flex';
        div.innerHTML = `
            <input type="text" class="form-control" name="permissions[]" placeholder="Enter permission" required>
            <button type="button" class="remove-btn ml-2" onclick="removeInput(this)">Remove</button>
        `;
        container.appendChild(div);
    }

    function removeInput(button) {
        const container = button.parentElement.parentElement;
        if (container.children.length > 1) {
            button.parentElement.remove();
        }
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/subscription-plans/create.blade.php ENDPATH**/ ?>