<!DOCTYPE html>
<html data-wf-domain="/" data-wf-page="663d095d8e07618c5381377b" data-wf-site="663b748d327c826a2952af46" data-wf-status="1" lang="en" class=" w-mod-js w-mod-ix">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>EVOLUTION888 business center - Personal Dashboard</title>

  <!-- CSRF Token (Laravel) -->
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <!-- Main CSS -->
  <link rel="stylesheet" href="<?php echo e(asset('css/style.css')); ?>" />
  <link href="css/techbetatemplates.webflow.14600f26e.css?v=0.3" rel="stylesheet" type="text/css">

  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <!-- ApexCharts -->
  <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
  <!-- D3.js -->
  <script src="https://d3js.org/d3.v6.min.js"></script>
  <!-- QRCode.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
  <!-- Cytoscape.js -->
  <script src="https://unpkg.com/cytoscape/dist/cytoscape.min.js"></script>
  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

  <style>
    /* -------------------------------
       Общие стили
    -------------------------------- */
    .swal2-container {
      z-index: 100000 !important;
    }
    .balance-highlight {
      font-size: 18px;
      margin-bottom: 15px;
      padding: 10px 20px;
      border: 2px dashed #333;
      border-radius: 8px;
      display: inline-block;
      text-align: center;
      background-color: #f8f8f8;
    }
    .modal-backdrop {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(2px);
      justify-content: center;
      align-items: center;
    }
    .modal-content {
      background-color: #fff;
      border-radius: 12px;
      width: 620px;
      max-width: 95%;
      padding: 30px;
      box-shadow: 0 8px 30px rgba(0,0,0,0.15);
      border: 1px solid #e0e0e0;
      position: relative;
      overflow: hidden;
      animation: scaleIn 0.3s ease;
    }
    @keyframes scaleIn {
      from { transform: scale(0.9); opacity: 0; }
      to   { transform: scale(1); opacity: 1; }
    }
    .modal-title {
      font-size: 22px;
      margin-bottom: 20px;
      text-align: center;
      font-weight: bold;
    }
    .modal-close-btn {
      position: absolute;
      top: 15px;
      right: 15px;
      border: none;
      background: none;
      font-size: 22px;
      cursor: pointer;
      color: #999;
      transition: color 0.2s;
    }
    .modal-close-btn:hover {
      color: #333;
    }
    .modal-buttons {
      margin-top: 20px;
      display: flex;
      gap: 10px;
      justify-content: center;
    }

    /* -------------------------------
       Кнопки
    -------------------------------- */
    .btn-primary {
      background-color: #6366f1;
      color: #fff;
      border: none;
      padding: 10px 18px;
      border-radius: 6px;
      cursor: pointer;
      transition: background 0.2s;
    }
    .btn-primary:hover {
      background-color: #4f51c3;
    }
    .btn-primary:disabled {
      background-color: #ccc !important;
      cursor: not-allowed !important;
    }
    .btn-danger {
      background-color: #999;
      color: #fff;
      border: none;
      padding: 10px 18px;
      border-radius: 6px;
      cursor: pointer;
      transition: background 0.2s;
    }
    .btn-danger:hover {
      background-color: #666;
    }
    .btn-neutral {
      background-color: #fff;
      color: #333;
      border: 1px solid #ccc;
      padding: 10px 18px;
      border-radius: 6px;
      cursor: pointer;
      transition: background 0.2s;
    }
    .btn-neutral:hover {
      background-color: #f1f1f1;
    }

    /* -------------------------------
       KYC стили
    -------------------------------- */
    .kyc-status-block {
      font-size: 16px;
      text-align: center;
      margin-bottom: 20px;
      background: #f8f8f8;
      padding: 15px;
      border-radius: 8px;
    }
    .kyc-progress {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      padding: 0 20px;
      position: relative;
    }
    .kyc-progress::before {
      content: "";
      position: absolute;
      top: 14px;
      left: 40px;
      right: 40px;
      height: 2px;
      background: #ddd;
      z-index: 1;
    }
    .kyc-progress-step {
      position: relative;
      z-index: 2;
      text-align: center;
    }
    .kyc-progress-step .circle {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background-color: #ddd;
      margin: 0 auto 5px;
      font-size: 14px;
      line-height: 28px;
      color: #fff;
      font-weight: bold;
      transition: background 0.3s;
    }
    .kyc-progress-step.active .circle {
      background-color: #6366f1;
    }
    .kyc-progress-step span {
      font-size: 13px;
      color: #666;
      transition: color 0.3s;
    }
    .kyc-progress-step.active span {
      color: #6366f1;
      font-weight: 600;
    }
    .kyc-step {
      display: none;
      animation: fadeInUp 0.3s ease;
    }
    .kyc-step.active {
      display: block;
    }
    @keyframes fadeInUp {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .kyc-field-label {
      display: block;
      margin: 12px 0 6px;
      font-weight: 600;
    }
    .kyc-field-input {
      width: 100%;
      margin-bottom: 12px;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 6px;
    }

    /* -------------------------------
       Спиннер (лоадер)
    -------------------------------- */
    .spinner {
      display: inline-block;
      width: 18px;
      height: 18px;
      border: 2px solid #ccc;
      border-top-color: #6366f1;
      border-radius: 50%;
      animation: spin 0.6s linear infinite;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    /* -------------------------------
       Зеркалирование <video> (KYC)
    -------------------------------- */
    video.mirrored {
      transform: scaleX(-1);
    }
  </style>
</head>
<body>
<div class="page-wrapper">

  <!-- ---------- Navbar (если нужно, замените на HTML) ---------- -->
  <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

  <!-- ---------- Основной контент (ЛК, баланс, график, транзакции) ---------- -->
  <section class="section">
    <div class="w-layout-blockcontainer container-default w-container">
      <div class="w-layout-grid grid-1-column">
        <div class="w-layout-grid grid-2-columns features-grid-top---v1" style="gap: 40px; align-items: start;">

          <!-- Левый блок: пользователь -->
          <div class="card feature-card-v1" style="display: flex; flex-direction: column; align-items: center; padding: 30px; border: 1px solid #e0e0e0; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <h3 class="display-6 mid" style="margin-bottom: 20px;">
              <span id="userEmail">Loading...</span>
            </h3>
            <img id="userAvatar" src="/images/default-avatar.png" alt="User Avatar"
                 style="width: 80px; height: 80px; border-radius: 50%; margin-bottom: 20px; border: 2px solid #ddd;" />
            <div class="balance-highlight">
              <span id="userBalance">Loading...</span>
            </div>
            <!-- Кнопки -->
            <div style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;">
              <a href="/profile" class="primary-button dark-mode w-inline-block">
                <div class="text-block">Profile</div>
              </a>
              <a href="#" class="primary-button dark-mode w-inline-block" onclick="openTransferModal()">
                <div class="text-block">Transfer</div>
              </a>
              <a href="#" class="primary-button dark-mode w-inline-block" onclick="openWithdrawModal()">
                <div class="text-block">Withdraw</div>
              </a>
              <a href="#" class="primary-button dark-mode w-inline-block" onclick="openRedeemModal()">
                <div class="text-block">Vouchers</div>
              </a>
              <a href="/exchange" class="primary-button dark-mode w-inline-block">
                <div class="text-block">Buy voucher</div>
              </a>
              <a href="/shop" class="primary-button dark-mode w-inline-block">
                <div class="text-block">Certificates</div>
              </a>
              <a href="/dashboard/business-cards" class="primary-button dark-mode w-inline-block">
                <div class="text-block">Business Card</div>
              </a>
              <a href="/dashboard/club-profile" class="primary-button dark-mode w-inline-block">
                <div class="text-block">Club Profile</div>
              </a>
              <a href="#" class="primary-button dark-mode w-inline-block" onclick="openKycModal()">
                <div class="text-block">Verification</div>
              </a>
            </div>

            <!-- Referral link with QR -->
            <div style="margin-top: 25px; text-align: center; width: 100%;">
              <div style="display: flex; align-items: center; justify-content: center; gap: 12px; flex-wrap: wrap;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 3px; border-radius: 12px; flex: 1; min-width: 280px; max-width: 300px;">
                  <div id="referralLink"
                       style="display: flex; flex-direction: column; justify-content: center; background: #fff; padding: 12px 16px; border-radius: 10px; cursor: pointer; color: #333; text-align: center; transition: all 0.3s ease; height: 50px;"
                       onclick="copyReferralLink()" title="Click to copy">
                    <div style="font-weight: 600; color: #667eea; margin-bottom: 4px; font-size: 10px;">Your Referral Link</div>
                    <div style="font-family: monospace; line-height: 1.2; font-size: 10px;">
                      evolution888.org/referral/<span id="userInviteCode">Loading...</span>
                    </div>
                  </div>
                </div>
                <button onclick="openQRModal()" style="background: #6366f1; border: none; color: white; padding: 12px; border-radius: 12px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: background 0.3s ease; width: 56px; height: 56px;" title="Show QR code">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="3" width="7" height="7"></rect>
                    <rect x="14" y="3" width="7" height="7"></rect>
                    <rect x="14" y="14" width="7" height="7"></rect>
                    <line x1="3" y1="10" x2="3" y2="21"></line>
                    <line x1="10" y1="3" x2="10" y2="10"></line>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <!-- /Левый блок -->

          <!-- Правый блок: график, рефералы -->
          <div class="card feature-card-v1" style="padding: 30px; border: 1px solid #e0e0e0; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <div id="chart" class="mg-top-medium" style="margin: auto; width: 100%;"></div>
            <div style="text-align: center; margin-top: 20px;">
              <button onclick="openReferralTreeModal()" class="primary-button" style="padding: 10px 20px; font-size: 16px;">
                Referral Tree
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- История транзакций -->
    <div class="container" style="max-width: 800px; margin: 0 auto; margin-top: 10px;">
      <div class="transactions-card" style="padding: 30px; border: 1px solid #e0e0e0; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); background-color: #fff; margin-top: 20px;">
        <h3 style="font-size: 20px; margin-bottom: 20px; text-align: center;">Transaction History</h3>
        <table style="width: 100%; border-collapse: collapse; font-size: 16px;">
          <thead>
            <tr style="background-color: #f9f9f9;">
              <th style="border-bottom: 1px solid #e0e0e0; padding: 15px; text-align: left;">Date</th>
              <th style="border-bottom: 1px solid #e0e0e0; padding: 15px; text-align: left;">Description</th>
              <th style="border-bottom: 1px solid #e0e0e0; padding: 15px; text-align: left;">Amount</th>
              <th style="border-bottom: 1px solid #e0e0e0; padding: 15px; text-align: center;">Receipt</th>
            </tr>
          </thead>
          <tbody id="transaction-rows">
            <tr>
              <td colspan="4" style="text-align: center; padding: 20px;">Loading...</td>
            </tr>
          </tbody>
        </table>
        <div id="pagination" style="margin-top: 20px; text-align: center; display: flex; justify-content: center; gap: 10px;"></div>
      </div>
    </div>
  </section>

  <!-- ---------- Transfer Modal ---------- -->
  <div id="transferModal" class="modal-backdrop">
    <div class="modal-content">
      <button class="modal-close-btn" onclick="closeTransferModal()">×</button>
      <h2 class="modal-title">Transfer Tokens</h2>
      <div style="text-align: center; font-size: 14px; color: #666; margin-bottom: 10px;">
        Maximum available: <span id="transferMaxBalance">Loading...</span>
      </div>
      <label for="transferEmail" style="font-weight: 600; margin-top: 10px;">Recipient Email</label>
      <input type="email" id="transferEmail" class="input w-input" placeholder="Recipient Email" required style="width: 100%; margin-bottom: 10px;" />
      <label for="transferAmount" style="font-weight: 600;">Transfer Amount</label>
      <input type="number" id="transferAmount" class="input w-input" placeholder="Enter amount" required style="width: 100%; margin-bottom: 20px;" />
      <div class="modal-buttons">
        <button onclick="doTransfer()" class="btn-primary">Send</button>
        <button onclick="closeTransferModal()" class="btn-danger">Close</button>
      </div>
    </div>
  </div>

  <!-- ---------- Withdraw Modal ---------- -->
  <div id="withdrawModal" class="modal-backdrop">
    <div class="modal-content" style="max-width: 400px; text-align: center;">
      <button class="modal-close-btn" onclick="closeWithdrawModal()">×</button>
      <h2 class="modal-title">Withdraw Funds</h2>
      <div style="margin-bottom: 20px; text-align: left;">
        <label style="font-weight: 600; display: block; margin-bottom: 5px;">Select payment method:</label>
        <div id="paymentMethodsContainer" style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;"></div>
      </div>
      <div style="margin-bottom: 20px;">
        <label for="withdrawAddress" style="font-weight: 600;">Your withdrawal address</label>
        <input type="text" id="withdrawAddress" class="input w-input" placeholder="Wallet address" style="width: 100%; margin-bottom: 10px;" />
      </div>
      <div style="margin-bottom: 20px;">
        <label for="withdrawAmount" style="font-weight: 600;">Withdrawal amount (USDZ) 
          <div id='withdraw_blanace'></div>
        </label>
        <input type="number" id="withdrawAmount" class="input w-input" placeholder="Enter amount" style="width: 100%; margin-bottom: 10px;" />
      </div>
      <div style="margin-bottom: 20px;">
        <input type="range" id="withdrawSlider" min="0" max="100" value="0" style="width: 100%;" />
      </div>
      <div class="modal-buttons">
        <button onclick="doWithdraw()" class="btn-primary">Withdraw</button>
        <button onclick="closeWithdrawModal()" class="btn-danger">Close</button>
      </div>
    </div>
  </div>

  <!-- ---------- Redeem Modal ---------- -->
  <div id="redeemModal" class="modal-backdrop">
    <div class="modal-content">
      <button class="modal-close-btn" onclick="closeRedeemModal()">×</button>
      <h2 class="modal-title">Use Voucher</h2>
      <p style="text-align: center; font-size: 14px; color: #666;">Please enter your voucher code</p>
      <label for="redeemCode" style="font-weight: 600;">Code</label>
      <input type="text" id="redeemCode" class="input w-input" placeholder="XXXX-XXXX-XXXX-XXXX" required style="width: 100%; margin-bottom: 20px;" />
      <div class="modal-buttons">
        <button onclick="doRedeem()" class="btn-primary">Redeem</button>
        <button onclick="closeRedeemModal()" class="btn-danger">Close</button>
      </div>
    </div>
  </div>

  <!-- ---------- KYC Modal (зеркальное видео) ---------- -->
  <div id="kycModal" class="modal-backdrop">
    <div class="modal-content">
      <button class="modal-close-btn" onclick="closeKycModal()">×</button>
      <h2 class="modal-title">KYC Verification</h2>
      <div id="kycStatusBlock" class="kyc-status-block">Loading...</div>
      <div id="kycProgress" class="kyc-progress">
        <div class="kyc-progress-step" id="stepIndicator1">
          <div class="circle">1</div>
          <span>Full Name</span>
        </div>
        <div class="kyc-progress-step" id="stepIndicator2">
          <div class="circle">2</div>
          <span>Passport (main page)</span>
        </div>
        <div class="kyc-progress-step" id="stepIndicator3">
          <div class="circle">3</div>
          <span>Passport (registration page)</span>
        </div>
        <div class="kyc-progress-step" id="stepIndicator4">
          <div class="circle">4</div>
          <span>Selfie</span>
        </div>
      </div>
      <form id="kycForm" style="display: none;">
        <!-- Step 1: Full Name -->
        <div class="kyc-step" id="kycStep1">
          <label class="kyc-field-label" for="fullName">Your Full Name:</label>
          <input type="text" id="fullName" name="full_name" class="kyc-field-input"
                 placeholder="Example: John Smith Doe" required />
        </div>

        <!-- Step 2: Passport photo (main page) -->
        <div class="kyc-step" id="kycStep2">
          <label class="kyc-field-label">Passport photo (main page):</label>
          <div style="text-align:center;">
            <button type="button" class="btn-primary" id="startCameraPassportFrontBtn" onclick="startCamera('passportFront')">
              Turn on camera
            </button>
            <button type="button" class="btn-neutral" style="display:none;" id="flipCameraPassportFrontBtn" onclick="flipCamera('passportFront')">
              Flip camera
            </button>
            <button type="button" class="btn-danger" style="display:none;" id="stopCameraPassportFrontBtn" onclick="stopCamera('passportFront')">
              Turn off camera
            </button>
          </div>
          <div id="cameraPassportFront" style="display: none; text-align: center; margin-top: 10px;">
            <video id="videoPassportFront" autoplay playsinline class="mirrored"
                   style="width: 100%; max-width: 300px; border:1px solid #ccc;"></video>
            <div style="margin-top:10px;">
              <button type="button" class="btn-primary" onclick="takePhoto('passportFront')">
                Take photo
              </button>
            </div>
          </div>
          <div id="previewContainerPassportFront" style="display:none; text-align:center; margin-top:10px;">
            <canvas id="previewCanvasPassportFront"
                    style="max-width:300px; border:1px solid #ccc;"></canvas>
            <div style="margin-top:10px;">
              <button type="button" class="btn-primary" onclick="confirmPhoto('passportFront')">
                Confirm
              </button>
              <button type="button" class="btn-danger" onclick="retakePhoto('passportFront')">
                Retake photo
              </button>
            </div>
          </div>
          <div id="passportFrontLoader" style="display:none;"></div>
        </div>

        <!-- Step 3: Passport photo (registration page) -->
        <div class="kyc-step" id="kycStep3">
          <label class="kyc-field-label">Passport photo (registration page):</label>
          <div style="text-align:center;">
            <button type="button" class="btn-primary" id="startCameraPassportBackBtn" onclick="startCamera('passportBack')">
              Turn on camera
            </button>
            <button type="button" class="btn-neutral" style="display:none;" id="flipCameraPassportBackBtn" onclick="flipCamera('passportBack')">
              Flip camera
            </button>
            <button type="button" class="btn-danger" style="display:none;" id="stopCameraPassportBackBtn" onclick="stopCamera('passportBack')">
              Turn off camera
            </button>
          </div>
          <div id="cameraPassportBack" style="display: none; text-align: center; margin-top: 10px;">
            <video id="videoPassportBack" autoplay playsinline class="mirrored"
                   style="width: 100%; max-width: 300px; border:1px solid #ccc;"></video>
            <div style="margin-top:10px;">
              <button type="button" class="btn-primary" onclick="takePhoto('passportBack')">
                Take photo
              </button>
            </div>
          </div>
          <div id="previewContainerPassportBack" style="display:none; text-align:center; margin-top:10px;">
            <canvas id="previewCanvasPassportBack"
                    style="max-width:300px; border:1px solid #ccc;"></canvas>
            <div style="margin-top:10px;">
              <button type="button" class="btn-primary" onclick="confirmPhoto('passportBack')">
                Confirm
              </button>
              <button type="button" class="btn-danger" onclick="retakePhoto('passportBack')">
                Retake photo
              </button>
            </div>
          </div>
          <div id="passportBackLoader" style="display:none;"></div>
        </div>

        <!-- Step 4: Selfie -->
        <div class="kyc-step" id="kycStep4">
          <label class="kyc-field-label">Photo of yourself with passport (selfie):</label>
          <div style="text-align:center;">
            <button type="button" class="btn-primary" id="startCameraPassportSelfieBtn" onclick="startCamera('passportSelfie')">
              Turn on camera
            </button>
            <button type="button" class="btn-neutral" style="display:none;" id="flipCameraPassportSelfieBtn" onclick="flipCamera('passportSelfie')">
              Flip camera
            </button>
            <button type="button" class="btn-danger" style="display:none;" id="stopCameraPassportSelfieBtn" onclick="stopCamera('passportSelfie')">
              Turn off camera
            </button>
          </div>
          <div id="cameraPassportSelfie" style="display: none; text-align: center; margin-top: 10px;">
            <video id="videoPassportSelfie" autoplay playsinline class="mirrored"
                   style="width: 100%; max-width: 300px; border:1px solid #ccc;"></video>
            <div style="margin-top:10px;">
              <button type="button" class="btn-primary" onclick="takePhoto('passportSelfie')">
                Take photo
              </button>
            </div>
          </div>
          <div id="previewContainerPassportSelfie" style="display:none; text-align:center; margin-top:10px;">
            <canvas id="previewCanvasPassportSelfie"
                    style="max-width:300px; border:1px solid #ccc;"></canvas>
            <div style="margin-top:10px;">
              <button type="button" class="btn-primary" onclick="confirmPhoto('passportSelfie')">
                Confirm
              </button>
              <button type="button" class="btn-danger" onclick="retakePhoto('passportSelfie')">
                Retake photo
              </button>
            </div>
          </div>
          <div id="passportSelfieLoader" style="display:none;"></div>
        </div>

        <div class="modal-buttons" style="justify-content: space-between;">
          <button type="button" class="btn-danger" id="kycPrevBtn" onclick="prevKycStep()">Back</button>
          <button type="button" class="btn-primary" id="kycNextBtn" onclick="nextKycStep()">Next</button>
          <button type="button" class="btn-primary" id="kycSubmitBtn" onclick="submitKyc()" style="display: none;">
            Submit
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- ---------- Referral Tree Modal ---------- -->
  <div id="referralTreeModal" class="modal-backdrop">
    <div class="modal-content" style="width: 90%; max-width: 800px; position: relative;">
      <button class="modal-close-btn" onclick="closeReferralTreeModal()">×</button>
      <h2 class="modal-title">Referral Tree</h2>
      <!-- Контейнер для дерева Cytoscape -->
      <div id="referralTreeContainer" style="height: 600px;"></div>
    </div>
  </div>

  <!-- ---------- QR Code Modal ---------- -->
  <div id="qrModal" class="modal-backdrop">
    <div class="modal-content" style="width: 300px; text-align: center; position: relative;">
      <button class="modal-close-btn" onclick="closeQRModal()">×</button>
      <h2 class="modal-title">QR Code</h2>
      <div id="qrCodeContainer" style="margin: auto; width: 200px; height: 200px;"></div>
    </div>
  </div>

  <!-- ---------- Receipt Modal ---------- -->
  <div id="receiptModal" class="modal-backdrop">
    <div class="modal-content" style="max-width: 400px; text-align: left;">
      <button class="modal-close-btn" onclick="closeReceiptModal()">×</button>
      <h2 class="modal-title">Transaction Receipt</h2>
      <div id="receiptContent" style="font-size: 14px; color: #333; line-height: 1.6;"></div>
      <div class="modal-buttons" style="justify-content: center; margin-top: 20px;">
        <button onclick="closeReceiptModal()" class="btn-danger">Close</button>
      </div>
    </div>
  </div>
</div>

<script>
  /* 
   |===========================
   | ГЛОБАЛЬНЫЕ ПЕРЕМЕННЫЕ
   |===========================
  */
  let selectedPaymentMethod = null;
  let userBalanceAvailable  = 0;
  let currentKycStep        = 1;

  // Храним ID загруженных файлов
  let kycFileHashes = {
    passportFront: null,
    passportBack: null,
    passportSelfie: null
  };
  // Флаги загрузки
  let kycFileUploading = {
    passportFront: false,
    passportBack: false,
    passportSelfie: false
  };
  // Текущие стримы
  let cameraStreams = {
    passportFront: null,
    passportBack: null,
    passportSelfie: null
  };
  // Камера фронт/тыльная
  let cameraMode = {
    passportFront: 'environment',
    passportBack: 'environment',
    passportSelfie: 'user'
  };
  // Снимки (blob)
  let photoBlobs = {
    passportFront: null,
    passportBack: null,
    passportSelfie: null
  };

  /*
   |===========================
   | ФУНКЦИИ ОТКРЫТИЯ/ЗАКРЫТИЯ МОДАЛОК
   |===========================
  */
  function openTransferModal() {
    document.getElementById('transferModal').style.display = 'flex';
  }
  function closeTransferModal() {
    document.getElementById('transferModal').style.display = 'none';
  }

  function openWithdrawModal() {
    document.getElementById('withdrawModal').style.display = 'flex';
  }
  function closeWithdrawModal() {
    document.getElementById('withdrawModal').style.display = 'none';
  }

  function openRedeemModal() {
    document.getElementById('redeemModal').style.display = 'flex';
  }
  function closeRedeemModal() {
    document.getElementById('redeemModal').style.display = 'none';
  }

  function openKycModal() {
    document.getElementById('kycModal').style.display = 'flex';
    checkKycStatus();
  }
  function closeKycModal() {
    document.getElementById('kycModal').style.display = 'none';
  }

  function openReferralTreeModal() {
    document.getElementById('referralTreeModal').style.display = 'flex';
    loadReferralTree();
  }
  function closeReferralTreeModal() {
    document.getElementById('referralTreeModal').style.display = 'none';
  }

  function openQRModal() {
    document.getElementById('qrModal').style.display = 'flex';
    document.getElementById('qrCodeContainer').innerHTML = '';
    const inviteCode = document.getElementById('userInviteCode').innerText;
    const referralLink = "https://evolution888.org/referral/" + inviteCode;
    new QRCode(document.getElementById("qrCodeContainer"), {
      text: referralLink,
      width: 200,
      height: 200,
      colorDark : "#000000",
      colorLight : "#ffffff",
      correctLevel : QRCode.CorrectLevel.H
    });
  }
  function closeQRModal() {
    document.getElementById('qrModal').style.display = 'none';
  }

  function openReceiptModal(encodedTx) {
    const tx = JSON.parse(decodeURIComponent(encodedTx));
    const receiptContent = document.getElementById('receiptContent');
    receiptContent.innerHTML = `
      <p><strong>ID:</strong> ${tx._id}</p>
      <p><strong>User ID:</strong> ${tx.user_id}</p>
      <p><strong>Amount:</strong> ${tx.amount} USDZ</p>
      <p><strong>Description:</strong> ${tx.description}</p>
      <p><strong>Created At:</strong> ${new Date(tx.created_at).toLocaleString()}</p>
    `;
    document.getElementById('receiptModal').style.display = 'flex';
  }
  function closeReceiptModal() {
    document.getElementById('receiptModal').style.display = 'none';
  }

  /*
   |===========================
   | ИНФО О ПОЛЬЗОВАТЕЛЕ
   |===========================
  */
  async function getUserInfo() {
    try {
      const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
      const response = await fetch('/api/get_user_info', {
        headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': csrfToken }
      });
      if (!response.ok) throw new Error("Failed to fetch user info.");
      const data = await response.json();
      document.getElementById('userEmail').innerText = data.email;
      document.getElementById('userBalance').innerText = data.balance + " USDZ tokens";
      document.getElementById('userAvatar').src = data.avatar;
      document.getElementById('userInviteCode').innerText = data.invite_code;
      document.getElementById('transferMaxBalance').innerText = data.balance + " USDZ tokens";

      userBalanceAvailable = data.balance_avaliable || 0;
      document.getElementById('withdrawSlider').max = data.balance_avaliable;
      document.getElementById('withdraw_blanace').innerText = "Max " + data.balance_avaliable + " USDZ Tokens";
    } catch (error) {
      console.error(error);
    }
  }
  document.addEventListener('DOMContentLoaded', function () {
    getUserInfo();

    const slider = document.getElementById('withdrawSlider');
    const amountInput = document.getElementById('withdrawAmount');

    slider.addEventListener('input', function() {
      amountInput.value = this.value;
    });
    amountInput.addEventListener('input', function() {
      slider.value = this.value;
    });
  });

  /*
   |===========================
   | КОПИРОВАНИЕ РЕФ-ССЫЛКИ
   |===========================
  */
  function copyReferralLink() {
    const inviteCode = document.getElementById('userInviteCode').innerText;
    const fullLink = "https://evolution888.org/referral/" + inviteCode;
    const tempInput = document.createElement("input");
    tempInput.value = fullLink;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand("copy");
    document.body.removeChild(tempInput);

    Swal.fire({
      icon: 'success',
      title: 'Link copied',
      text: 'Your referral link has been copied to clipboard!',
      confirmButtonText: 'OK'
    });
  }

  /*
   |===========================
   | ТРАНСФЕР
   |===========================
  */
  async function doTransfer() {
    try {
      const email  = document.getElementById('transferEmail').value;
      const amount = document.getElementById('transferAmount').value;
      if (!email || !amount || parseFloat(amount) <= 0) {
        Swal.fire({
          icon: 'warning',
          title: 'Invalid data',
          text: 'Please enter a valid email and amount.',
          confirmButtonText: 'OK'
        });
        return;
      }

      Swal.fire({
        title: 'Transfer confirmation',
        text: `Are you sure you want to transfer ${amount} USDZ to ${email}?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Yes, transfer',
        cancelButtonText: 'Cancel'
      }).then(async (result) => {
        if (result.isConfirmed) {
          const payload = { email, amount };
          const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
          const response = await fetch('/api/transfer', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': csrfToken },
            body: JSON.stringify(payload)
          });

          if (!response.ok) {
            const data = await response.json();
            throw new Error(data.message || 'Error occurred while transferring tokens.');
          }

          closeTransferModal();
          document.getElementById('transferEmail').value = '';
          document.getElementById('transferAmount').value = '';
          getUserInfo();

          Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Transfer completed!',
            confirmButtonText: 'OK'
          });
        }
      });
    } catch (error) {
      console.error(error);
      Swal.fire({
        icon: 'error',
        title: 'Transfer error',
        text: error.message || 'An error occurred during transfer.',
        confirmButtonText: 'OK'
      });
    }
  }

  /*
   |===========================
   | ВЫВОД
   |===========================
  */
  async function doWithdraw() {
    try {
      if (!selectedPaymentMethod) {
        Swal.fire({
          icon: 'warning',
          title: 'Не выбран метод',
          text: 'Пожалуйста, выберите способ оплаты.',
          confirmButtonText: 'OK'
        });
        return;
      }
      const address = document.getElementById('withdrawAddress').value.trim();
      if (!address) {
        Swal.fire({
          icon: 'warning',
          title: 'Пустой адрес',
          text: 'Укажите адрес для вывода.',
          confirmButtonText: 'OK'
        });
        return;
      }
      const amount = document.getElementById('withdrawAmount').value;
      if (!amount || parseFloat(amount) <= 0) {
        Swal.fire({
          icon: 'warning',
          title: 'Неверная сумма',
          text: 'Укажите сумму больше 0.',
          confirmButtonText: 'OK'
        });
        return;
      }
      if (parseFloat(amount) < parseFloat(selectedPaymentMethod.min_amount)) {
        Swal.fire({
          icon: 'warning',
          title: 'Слишком мало',
          text: `Минимальная сумма: ${selectedPaymentMethod.min_amount} USDZ.`,
          confirmButtonText: 'OK'
        });
        return;
      }
      if (parseFloat(amount) > parseFloat(userBalanceAvailable)) {
        Swal.fire({
          icon: 'warning',
          title: 'Недостаточно средств',
          text: 'Сумма вывода превышает доступный баланс.',
          confirmButtonText: 'OK'
        });
        return;
      }

      Swal.fire({
        title: 'Подтверждение вывода',
        text: `Вы уверены, что хотите вывести ${amount} USDZ на адрес: ${address}?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Да, вывести',
        cancelButtonText: 'Отмена'
      }).then(async (result) => {
        if (result.isConfirmed) {
          const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
          const payload = {
            payment_method: selectedPaymentMethod._id,
            address,
            amount
          };
          const response = await fetch('/api/withdraw', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': csrfToken },
            body: JSON.stringify(payload)
          });
          if (!response.ok) {
            const data = await response.json();
            throw new Error(data.message || 'Ошибка при создании заявки на вывод.');
          }

          closeWithdrawModal();
          document.getElementById('withdrawAddress').value = '';
          document.getElementById('withdrawAmount').value = '';
          document.getElementById('withdrawSlider').value = 0;
          selectedPaymentMethod = null;
          getUserInfo();

          Swal.fire({
            icon: 'success',
            title: 'Заявка создана',
            text: 'Ваша заявка на вывод успешно создана!',
            confirmButtonText: 'OK'
          });
        }
      });
    } catch (error) {
      console.error(error);
      Swal.fire({
        icon: 'error',
        title: 'Ошибка вывода',
        text: error.message || 'Что-то пошло не так при выводе средств.',
        confirmButtonText: 'OK'
      });
    }
  }

  /*
   |===========================
   | REDEEM (Ваучеры)
   |===========================
  */
  async function doRedeem() {
    try {
      const code = document.getElementById('redeemCode').value.trim();
      const pattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i;
      if (!pattern.test(code)) {
        Swal.fire({
          icon: 'warning',
          title: 'Неверный код',
          text: 'Укажите код в формате XXXX-XXXX-XXXX-XXXX',
          confirmButtonText: 'OK'
        });
        return;
      }

      const confirmResult = await Swal.fire({
        title: 'Подтверждение использования',
        text: `Вы уверены, что хотите использовать ваучер: ${code}?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Да, применить',
        cancelButtonText: 'Отмена'
      });

      if (!confirmResult.isConfirmed) {
        return;
      }

      const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
      const response = await fetch('/api/code/redeem', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': csrfToken },
        body: JSON.stringify({ code })
      });

      if (!response.ok) {
        const data = await response.json();
        if (data.status === 'error' && data.message === 'Invalid or expired code.') {
          throw new Error('Этот ваучер недействителен или срок действия истек.');
        }
        throw new Error(data.message || 'Ошибка при активации кода.');
      }

      closeRedeemModal();
      document.getElementById('redeemCode').value = '';
      getUserInfo();

      await Swal.fire({
        icon: 'success',
        title: 'Успешно',
        text: 'Ваучер успешно активирован!',
        confirmButtonText: 'OK'
      });

    } catch (error) {
      console.error(error);
      Swal.fire({
        icon: 'error',
        title: 'Ошибка',
        text: error.message || 'Не удалось активировать ваучер.',
        confirmButtonText: 'OK'
      });
    }
  }

  /*
   |===========================
   | KYC + CAMERA
   |===========================
  */
  function checkNextButtonState() {
    if (kycFileUploading.passportFront || kycFileUploading.passportBack || kycFileUploading.passportSelfie) {
      document.getElementById('kycNextBtn').disabled = true;
      return;
    }
    let canProceed = true;
    if (currentKycStep === 2 && !kycFileHashes.passportFront)  canProceed = false;
    if (currentKycStep === 3 && !kycFileHashes.passportBack)   canProceed = false;
    if (currentKycStep === 4 && !kycFileHashes.passportSelfie) canProceed = false;

    document.getElementById('kycNextBtn').disabled = !canProceed;
  }

  async function startCamera(field) {
    try {
      document.getElementById(`startCamera${capitalize(field)}Btn`).style.display = 'none';
      document.getElementById(`flipCamera${capitalize(field)}Btn`).style.display = 'inline-block';
      document.getElementById(`stopCamera${capitalize(field)}Btn`).style.display = 'inline-block';

      document.getElementById(`camera${capitalize(field)}`).style.display = 'block';
      document.getElementById(`previewContainer${capitalize(field)}`).style.display = 'none';

      stopCamera(field, false);

      const constraints = {
        video: {
          facingMode: { ideal: cameraMode[field] }
        },
        audio: false
      };
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      cameraStreams[field] = stream;
      const video = document.getElementById(`video${capitalize(field)}`);
      if (video) {
        video.srcObject = stream;
      }
    } catch (error) {
      console.error(error);
      Swal.fire({
        icon: 'error',
        title: 'Ошибка камеры',
        text: 'Не удалось открыть камеру. Возможно, нет разрешения или нет поддержки.',
        confirmButtonText: 'OK'
      });
      document.getElementById(`startCamera${capitalize(field)}Btn`).style.display = 'inline-block';
      document.getElementById(`flipCamera${capitalize(field)}Btn`).style.display = 'none';
      document.getElementById(`stopCamera${capitalize(field)}Btn`).style.display = 'none';
    }
  }

  function flipCamera(field) {
    cameraMode[field] = (cameraMode[field] === 'user') ? 'environment' : 'user';
    startCamera(field);
  }

  function stopCamera(field, hide = true) {
    const stream = cameraStreams[field];
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
    }
    cameraStreams[field] = null;

    if (hide) {
      document.getElementById(`camera${capitalize(field)}`).style.display = 'none';
    }
    document.getElementById(`startCamera${capitalize(field)}Btn`).style.display = 'inline-block';
    document.getElementById(`flipCamera${capitalize(field)}Btn`).style.display = 'none';
    document.getElementById(`stopCamera${capitalize(field)}Btn`).style.display = 'none';
  }

  function takePhoto(field) {
    const video = document.getElementById(`video${capitalize(field)}`);
    if (!video) return;

    const canvas = document.getElementById(`previewCanvas${capitalize(field)}`);
    const previewContainer = document.getElementById(`previewContainer${capitalize(field)}`);

    canvas.width = video.videoWidth;
    canvas.height= video.videoHeight;

    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    canvas.toBlob((blob) => {
      photoBlobs[field] = blob;
    }, 'image/jpeg', 0.9);

    document.getElementById(`camera${capitalize(field)}`).style.display = 'none';
    previewContainer.style.display = 'block';
  }

  async function confirmPhoto(field) {
    if (!photoBlobs[field]) return;

    const loaderEl = document.getElementById(field + 'Loader');
    if (loaderEl) {
      loaderEl.classList.add('spinner');
      loaderEl.style.display = 'inline-block';
    }
    kycFileUploading[field] = true;
    checkNextButtonState();

    const formData = new FormData();
    formData.append('file', photoBlobs[field], field + '.jpg');
    formData.append('field', field);

    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    try {
      const response = await fetch('/api/kyc/upload', {
        method: 'POST',
        headers: { 'X-CSRF-TOKEN': csrfToken, 'Accept': 'application/json' },
        body: formData
      });
      const data = await response.json();

      if (!response.ok) {
        let errorText = data.message || 'Ошибка при загрузке.';
        if (data.errors && data.errors.file) {
          errorText = data.errors.file.join('; ');
        }
        throw new Error(errorText);
      }

      kycFileHashes[field] = data.id;
      Swal.fire({
        icon: 'success',
        title: 'Фото загружено',
        text: 'Изображение успешно отправлено на сервер.',
        confirmButtonText: 'OK'
      });
    } catch (error) {
      console.error(error);
      Swal.fire({
        icon: 'error',
        title: 'Ошибка',
        text: error.message || 'Не удалось загрузить фото.',
        confirmButtonText: 'OK'
      });
    } finally {
      if (loaderEl) {
        loaderEl.classList.remove('spinner');
        loaderEl.style.display = 'none';
      }
      kycFileUploading[field] = false;
      checkNextButtonState();
    }
  }

  function retakePhoto(field) {
    document.getElementById(`previewContainer${capitalize(field)}`).style.display = 'none';
    document.getElementById(`camera${capitalize(field)}`).style.display = 'block';
    photoBlobs[field] = null;
  }

  async function checkKycStatus() {
    try {
      const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
      const response = await fetch('/api/kyc/status', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': csrfToken }
      });
      if (!response.ok) {
        throw new Error("Failed to fetch KYC status.");
      }
      const data = await response.json();

      const statusBlock = document.getElementById('kycStatusBlock');
      const kycForm     = document.getElementById('kycForm');
      const kycProgress = document.getElementById('kycProgress');

      kycForm.style.display     = 'block';
      kycProgress.style.display = 'flex';

      if (data.status === 'verified') {
        statusBlock.innerText = 'Ваш KYC уже подтверждён!';
        kycForm.style.display     = 'none';
        kycProgress.style.display = 'none';
      } else if (data.status === 'pending') {
        statusBlock.innerText = 'Ваша заявка уже в обработке. Пожалуйста, ожидайте проверки.';
        kycForm.style.display     = 'none';
        kycProgress.style.display = 'none';
      } else if (data.status === 'rejected') {
        statusBlock.innerText = 'KYC отклонён. Пожалуйста, загрузите корректные данные повторно.';
      } else {
        statusBlock.innerText = 'Чтобы начать верификацию, заполните данные ниже по шагам.';
      }

      if (data.status === 'rejected' || data.status === 'unverified') {
        currentKycStep = 1;
        showKycStep(currentKycStep);
      }
    } catch (error) {
      console.error(error);
    }
  }

  function showKycStep(step) {
    stopCamera('passportFront', false);
    stopCamera('passportBack', false);
    stopCamera('passportSelfie', false);

    for (let i = 1; i <= 4; i++) {
      const stepDiv = document.getElementById(`kycStep${i}`);
      const indicator = document.getElementById(`stepIndicator${i}`);
      if (!stepDiv || !indicator) continue;

      if (i === step) {
        stepDiv.classList.add('active');
        indicator.classList.add('active');
      } else {
        stepDiv.classList.remove('active');
        indicator.classList.remove('active');
      }
    }
    document.getElementById('kycPrevBtn').style.display = (step === 1) ? 'none' : 'inline-block';
    document.getElementById('kycNextBtn').style.display = (step === 4) ? 'none' : 'inline-block';
    document.getElementById('kycSubmitBtn').style.display = (step === 4) ? 'inline-block' : 'none';

    checkNextButtonState();
  }

  function nextKycStep() {
    if (currentKycStep < 4) {
      currentKycStep++;
      showKycStep(currentKycStep);
    }
  }
  function prevKycStep() {
    if (currentKycStep > 1) {
      currentKycStep--;
      showKycStep(currentKycStep);
    }
  }

  async function submitKyc() {
    try {
      const fullName = document.getElementById('fullName').value.trim();
      if (!fullName ||
          !kycFileHashes.passportFront ||
          !kycFileHashes.passportBack ||
          !kycFileHashes.passportSelfie) {
        Swal.fire({
          icon: 'warning',
          title: 'Не все данные',
          text: 'Проверьте, что все шаги KYC завершены и поля заполнены.',
          confirmButtonText: 'OK'
        });
        return;
      }

      const formData = new FormData();
      formData.append('full_name', fullName);
      formData.append('documents[]', kycFileHashes.passportFront);
      formData.append('documents[]', kycFileHashes.passportBack);
      formData.append('documents[]', kycFileHashes.passportSelfie);

      const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
      const response = await fetch('/api/kyc/submit', {
        method: 'POST',
        headers: { 'X-CSRF-TOKEN': csrfToken },
        body: formData
      });
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || 'Не удалось отправить данные KYC');
      }

      Swal.fire({
        icon: 'success',
        title: 'Заявка отправлена',
        text: 'Ваша заявка отправлена, ожидайте проверки.',
        confirmButtonText: 'OK'
      });
      checkKycStatus();
    } catch (error) {
      console.error(error);
      Swal.fire({
        icon: 'error',
        title: 'Ошибка KYC',
        text: error.message || 'Что-то пошло не так при отправке заявки.',
        confirmButtonText: 'OK'
      });
    }
  }

  function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /*
   |===========================
   | ЗАГРУЗКА МЕТОДОВ ОПЛАТЫ
   |===========================
  */
  document.addEventListener('DOMContentLoaded', function() {
    function loadPaymentMethods() {
      $.ajax({
        url: '/api/payment-methods',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
          const container = document.getElementById('paymentMethodsContainer');
          if (!container) return;
          container.innerHTML = '';
          data.forEach(function(method) {
            const card = document.createElement('div');
            card.style.border = '1px solid #ddd';
            card.style.borderRadius = '6px';
            card.style.padding = '10px';
            card.style.cursor = 'pointer';
            card.style.textAlign = 'center';
            card.style.width = '100px';
            card.style.marginBottom = '10px';

            const img = document.createElement('img');
            img.src = method.img || '/images/default-payment.png';
            img.alt = method.name;
            img.style.maxWidth = '60px';
            img.style.marginBottom = '5px';
            card.appendChild(img);

            const title = document.createElement('div');
            title.style.fontWeight = 'bold';
            title.style.fontSize = '14px';
            title.textContent = method.name;
            card.appendChild(title);

            const minInfo = document.createElement('div');
            minInfo.style.fontSize = '12px';
            minInfo.style.color = '#666';
            minInfo.textContent = `Мин: ${method.min_amount} USDZ`;
            card.appendChild(minInfo);

            card.addEventListener('click', function() {
              const allCards = container.querySelectorAll('div');
              allCards.forEach(c => {
                c.style.borderColor = '#ddd';
                c.style.boxShadow = 'none';
              });
              card.style.borderColor = '#6366f1';
              card.style.boxShadow = '0 2px 8px rgba(99,102,241,0.2)';
              selectedPaymentMethod = method;
              document.getElementById('withdrawAddress').placeholder = method.example_address || 'Адрес кошелька';
              document.getElementById('withdrawAmount').min = method.min_amount;
            });

            container.appendChild(card);
          });
        }
      });
    }

    const oldOpenWithdrawModal = window.openWithdrawModal;
    window.openWithdrawModal = function() {
      oldOpenWithdrawModal();
      loadPaymentMethods();
      selectedPaymentMethod = null;
      document.getElementById('withdrawAddress').placeholder = 'Введите адрес для вывода';
      document.getElementById('withdrawAmount').min = 0;
    };
  });

  /*
   |===========================
   | ТРАНЗАКЦИИ, ГРАФИК, РЕФЕРАЛЫ
   |===========================
  */
  document.addEventListener('DOMContentLoaded', function () {
    function loadTransactions(page = 1) {
      $.ajax({
        url: `/api/get_transactions?page=${page}`,
        method: 'GET',
        success: function (response) {
          const rows = response.data.map(tx => `
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #e0e0e0;">
                ${new Date(tx.created_at).toLocaleDateString()}
              </td>
              <td style="padding: 10px; border-bottom: 1px solid #e0e0e0;">
                ${tx.description}
              </td>
              <td style="padding: 10px; border-bottom: 1px solid #e0e0e0;">
                ${tx.amount} USDZ
              </td>
              <td style="padding: 10px; border-bottom: 1px solid #e0e0e0; text-align: center;">
                <button onclick="openReceiptModal('${encodeURIComponent(JSON.stringify(tx))}')" title="Показать чек"
                  style="background: none; border: none; cursor: pointer;">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#6366f1" stroke-width="2"
                       stroke-linecap="round" stroke-linejoin="round">
                    <path d="M4 2h16v4H4z"></path>
                    <path d="M4 6v16l2-2 2 2 2-2 2 2 2-2 2 2 2-2v-16H4z"></path>
                  </svg>
                </button>
              </td>
            </tr>
          `).join('');
          document.getElementById('transaction-rows').innerHTML = rows;
          const paginationLinks = response.links.map(link => `
            <a href="#"
               data-page="${link.url ? new URL(link.url).searchParams.get('page') : null}"
               style="padding: 5px 10px; border: 1px solid #ddd; border-radius: 5px; text-decoration: none;
                      background-color: ${link.active ? '#ccc' : '#fff'}; color: #333;">
              ${link.label}
            </a>
          `).join('');
          document.getElementById('pagination').innerHTML = paginationLinks;
        },
        error: function () {
          document.getElementById('transaction-rows').innerHTML = `
            <tr>
              <td colspan="4" style="text-align: center; padding: 20px; color: red;">
                Failed to load transactions.
              </td>
            </tr>
          `;
        }
      });
    }
    loadTransactions();

    document.getElementById('pagination').addEventListener('click', function (e) {
      if (e.target.tagName === 'A') {
        e.preventDefault();
        const page = e.target.getAttribute('data-page');
        if (page) loadTransactions(page);
      }
    });
  });

  document.addEventListener('DOMContentLoaded', function () {
    const chartContainer = document.querySelector("#chart");
    chartContainer.innerHTML = '<div style="text-align: center; padding: 50px;">Loading...</div>';
    $.ajax({
      url: '/api/get_chart_info',
      method: 'GET',
      success: function (response) {
        const chartData = Object.values(response.chart_info || {});
        const total = response.total;
        chartContainer.innerHTML = '';
        const options = {
          chart: { type: 'bar', height: 350, toolbar: { show: false } },
          title: {
            text: 'Levels Chart',
            align: 'center',
            style: { fontSize: '20px' }
          },
          subtitle: {
            text: 'Total number of referrals: ' + total,
            align: 'center',
            style: { fontSize: '16px', color: '#666' }
          },
          series: [{ name: 'Users', data: chartData }],
          xaxis: {
            categories: ['Level 1','Level 2','Level 3','Level 4','Level 5','Level 6','Level 7'],
            labels: { style: { fontSize: '14px' } }
          },
          yaxis: {
            labels: { style: { fontSize: '14px' } },
            tickAmount: Math.max(...chartData),
            forceNiceScale: true
          },
          plotOptions: { bar: { borderRadius: 4, horizontal: false } },
          dataLabels: { enabled: false },
          grid: { borderColor: '#e0e0e0', strokeDashArray: 5 }
        };
        const chart = new ApexCharts(chartContainer, options);
        chart.render();
      },
      error: function () {
        chartContainer.innerHTML = '<div style="text-align: center; padding: 50px; color: red;">Error loading data</div>';
      }
    });
  });

  /* 
    |============================================
    | Изменённая функция загрузки реферального дерева
    | с использованием Cytoscape.js
    |============================================
  */
	function loadReferralTree() {
	  const container = document.getElementById('referralTreeContainer');
	  container.innerHTML = '';

	  fetch('/api/get_referal_tree')
		.then(response => response.json())
		.then(data => {
		  // Рекурсивно формируем массив элементов (узлы + рёбра)
		  function buildElements(node, parentId = null, elements = []) {
			const nodeId = node.name; // Предполагаем, что name (email) уникален
			elements.push({
			  data: {
				id: nodeId,
				label: node.name
			  }
			});
			if (parentId) {
			  elements.push({
				data: {
				  id: parentId + '_' + nodeId,
				  source: parentId,
				  target: nodeId
				}
			  });
			}
			if (node.children && node.children.length > 0) {
			  node.children.forEach(child => {
				buildElements(child, nodeId, elements);
			  });
			}
			return elements;
		  }

		  const elements = buildElements(data);

		  const cy = cytoscape({
			container: container,
			elements: elements,

			// Стили узлов и рёбер
			style: [
			  {
				selector: 'node',
				style: {
				  // Делаем узел «больше»: подгоняем размеры под текст + отступ
				  'shape': 'round-rectangle',
				  'width': 'label',
				  'height': 'label',
				  'padding': '10px',

				  // Внешний вид
				  'background-color': '#ffffff',
				  'border-color': '#007AFF',
				  'border-width': 2,
				  'border-opacity': 1,

				  // Текст (email)
				  'label': 'data(label)',
				  'color': '#000',           // чёрный текст
				  'text-wrap': 'wrap',       // перенос строк
				  'text-max-width': '150',   // макс. ширина текста (чтобы не растягивать узел слишком сильно)
				  'font-size': '14px',       // чуть увеличиваем шрифт
				  'text-valign': 'center',
				  'text-halign': 'center'
				}
			  },
			  {
				selector: 'edge',
				style: {
				  'width': 2,
				  'line-color': '#ccc',
				  'target-arrow-color': '#ccc',
				  'target-arrow-shape': 'triangle'
				}
			  }
			],

			// Раскладка (breadthfirst) с учётом размеров узлов и более плотным расположением
			layout: {
			  name: 'breadthfirst',
			  directed: true,
			  nodeDimensionsIncludeLabels: true, // учитывать размеры метки при вычислении положения
			  avoidOverlap: true,
			  avoidOverlapPadding: 10, // отступ между узлами
			  spacingFactor: 1.2,      // «коэффициент растяжения» (меньше → плотнее)
			  padding: 20              // отступы от краёв всей схемы
			}
		  });
		})
		.catch(error => {
		  console.error('Error fetching referral tree data:', error);
		  container.innerHTML = '<div style="text-align:center; padding: 20px; color:red;">Error loading data</div>';
		});
	}

</script>

<!-- ---------- Секция со слайдером ---------- -->
<section style="margin-top: -250px;" class="section">
  <div class="w-layout-blockcontainer container-default w-container">
    <div data-w-id="585590d1-3f51-4cd6-a924-172ba35490ce" style="transform: translate3d(0px, 0%, 0px); opacity: 1;" class="title-left---content-right small-content">
      <div class="inner-container _380px">
        <!-- Можно добавить дополнительный текст --> 
      </div>
    </div>
    <style>
    @media (max-width: 767px) {
      .mg-top-extra-large {
        margin-top: 75px;
      }
      .w-slider {
        width: 100%;
        height: auto;
        overflow: hidden;
      }
      .w-slider-mask {
        display: flex;
        flex-wrap: nowrap;
      }
      .w-slide {
        flex: 0 0 100%;
      }
      .w-slider-arrow-left, .w-slider-arrow-right {
        display: none;
      }
      .w-slider-nav {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
      }
    } 
    </style>
    <div class="mg-top-extra-large">
      <div data-w-id="2f3c6758-529c-1c4d-be0c-d21a74ed3487" style="opacity: 1;" class="card testimonial-card-wrapper---v1">
        <div class="w-slider slider" data-autoplay="false" data-delay="4000" data-animation="slide" data-duration="500" data-infinite="1" role="region" aria-label="Slider">
          <div class="w-slider-mask">
            <div class="w-slide">
              <a href="/dashboard" class="slider-link w-inline-block">
                <img src="images/evolution888_banner_1200x300_slide1.png" alt="EVOLUTION888 клуб - РЕГИСТРАЦИЯ" class="slider-image">
              </a>
            </div>
            <div class="w-slide">
              <a href="/dashboard" target="_blank" class="slider-link w-inline-block">
                <img src="images/evolution888_banner_1200x300_slide2.jpg" alt="EVOLUTION888 клуб - РЕГИСТРАЦИЯ" class="slider-image">
              </a>
            </div>
            <div class="w-slide">
              <a href="/dashboard" target="_blank" class="slider-link w-inline-block">
                <img src="images/evolution888_banner_1200x300_slide3.png" alt="EVOLUTION888 клуб - РЕГИСТРАЦИЯ" class="slider-image">
              </a>
            </div>
          </div>
          <div class="w-slider-arrow-left">
            <div class="w-icon-slider-left"></div>
          </div>
          <div class="w-slider-arrow-right">
            <div class="w-icon-slider-right"></div>
          </div>
          <div class="w-slider-nav w-round"></div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- ---------- Footer ---------- -->
<footer class="footer-wrapper" style="opacity: 1;">
  <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</footer>

<!-- jQuery (доп. подключение, если нужно) -->
<script src="<?php echo e(asset('js/jquery-3.5.1.min.dc5e7f18c8.js')); ?>" crossorigin="anonymous"></script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/dashboard/index.blade.php ENDPATH**/ ?>