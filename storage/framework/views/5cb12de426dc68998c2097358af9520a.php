<!DOCTYPE html>
<html data-wf-domain="/" data-wf-page="663d095d8e07618c5381377b" data-wf-site="663b748d327c826a2952af46" data-wf-status="1" lang="en" class=" w-mod-js w-mod-ix">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>EVOLUTION888 business center - My Club Profile</title>

  <!-- CSRF Token (Laravel) -->
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <!-- Main CSS -->
  <link href="/css/techbetatemplates.webflow.14600f26e.css?v=0.3" rel="stylesheet" type="text/css">

  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <style>
    .club-profile-hero {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 60px 0;
      margin-top: -120px;
      color: white;
    }

    .profile-card {
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
      margin-top: -40px;
      position: relative;
      z-index: 10;
    }

    .profile-header {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      padding: 40px;
      text-align: center;
      color: white;
    }

    .profile-avatar {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      border: 5px solid white;
      margin: 0 auto 20px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .profile-avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .profile-avatar i {
      font-size: 60px;
      line-height: 110px;
      color: #ddd;
    }

    .profile-body {
      padding: 40px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 30px;
      margin-bottom: 30px;
    }

    .info-item {
      background: #f8f9fa;
      padding: 25px;
      border-radius: 15px;
      border-left: 4px solid #667eea;
    }

    .info-label {
      font-size: 14px;
      color: #6c757d;
      margin-bottom: 8px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .info-value {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
    }

    .status-badge {
      display: inline-block;
      padding: 8px 16px;
      border-radius: 25px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-pending {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }

    .status-approved {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status-rejected {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;
    }

    .tag {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;
    }

    .action-buttons {
      display: flex;
      gap: 15px;
      justify-content: center;
      margin-top: 30px;
    }

    .btn-modern {
      padding: 12px 30px;
      border-radius: 25px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border: none;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary-modern {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
    }

    .btn-primary-modern:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
      color: white;
    }

    .btn-secondary-modern {
      background: linear-gradient(135deg, #f093fb, #f5576c);
      color: white;
    }

    .btn-secondary-modern:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(240, 147, 251, 0.4);
      color: white;
    }

    .btn-success-modern {
      background: linear-gradient(135deg, #4facfe, #00f2fe);
      color: white;
    }

    .btn-success-modern:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(79, 172, 254, 0.4);
      color: white;
    }

    .empty-state {
      text-align: center;
      padding: 80px 40px;
    }

    .empty-state i {
      font-size: 80px;
      color: #e9ecef;
      margin-bottom: 30px;
    }

    .empty-state h3 {
      color: #6c757d;
      margin-bottom: 15px;
    }

    .empty-state p {
      color: #adb5bd;
      font-size: 16px;
      margin-bottom: 30px;
    }

    .subscription-required {
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
      border-radius: 20px;
      padding: 40px;
      text-align: center;
      margin: 40px 0;
    }

    .subscription-required h3 {
      color: #721c24;
      margin-bottom: 15px;
    }

    .subscription-required p {
      color: #856404;
      margin-bottom: 25px;
    }
  </style>
</head>
<body>
<div class="page-wrapper">

  <!-- ---------- Navbar ---------- -->
  <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

  <!-- Hero Section -->
  <section class="club-profile-hero">
    <div class="w-layout-blockcontainer container-default w-container">
      <div class="text-center">
        <h1 class="display-4 mb-3">My Club Profile</h1>
        <p class="lead">Manage your business club presence and connect with other members</p>
      </div>
    </div>
  </section>

  <!-- Main Content -->
  <section class="section">
    <div class="w-layout-blockcontainer container-default w-container">

      <?php if(!auth()->user()->hasActiveSubscription()): ?>
        <div class="subscription-required">
          <i class="fas fa-lock fa-3x mb-4" style="color: #721c24;"></i>
          <h3>Subscription Required</h3>
          <p>You need an active subscription to create and manage your club profile. Join our business community today!</p>
          <a href="<?php echo e(route('subscriptions.plans')); ?>" class="btn-primary-modern btn-modern">
            <i class="fas fa-crown"></i> View Subscription Plans
          </a>
        </div>
      <?php elseif(!$profile): ?>
        <div class="profile-card">
          <div class="empty-state">
            <i class="fas fa-users"></i>
            <h3>No Club Profile Yet</h3>
            <p>Create your club profile to join our exclusive business community and connect with other professionals.</p>
            <a href="<?php echo e(route('dashboard.club-profile.create')); ?>" class="btn-primary-modern btn-modern">
              <i class="fas fa-plus"></i> Create Profile
            </a>
          </div>
        </div>
      <?php else: ?>
        <div class="profile-card">
          <div class="profile-header">
            <div class="profile-avatar">
              <?php if($profile->user->avatar): ?>
                <img src="<?php echo e($profile->user->avatar); ?>" alt="<?php echo e($profile->user->full_name ?? $profile->user->name); ?>">
              <?php else: ?>
                <i class="fas fa-user"></i>
              <?php endif; ?>
            </div>
            <h2><?php echo e($profile->profile_name); ?></h2>
            <p><?php echo e($profile->user->full_name ?? $profile->user->name); ?></p>
            <div class="status-badge status-<?php echo e($profile->moderation_status); ?>">
              <?php if($profile->moderation_status === 'pending'): ?>
                <i class="fas fa-clock"></i> Pending Review
              <?php elseif($profile->moderation_status === 'approved'): ?>
                <i class="fas fa-check-circle"></i> Approved
              <?php else: ?>
                <i class="fas fa-times-circle"></i> Rejected
              <?php endif; ?>
            </div>
          </div>

          <div class="profile-body">
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Description</div>
                <div class="info-value"><?php echo e($profile->description ?: 'No description provided'); ?></div>
              </div>

              <?php if($profile->website): ?>
              <div class="info-item">
                <div class="info-label">Website</div>
                <div class="info-value">
                  <a href="<?php echo e($profile->website); ?>" target="_blank" style="color: #667eea;">
                    <i class="fas fa-external-link-alt"></i> <?php echo e($profile->website); ?>

                  </a>
                </div>
              </div>
              <?php endif; ?>

              <?php if($profile->phone): ?>
              <div class="info-item">
                <div class="info-label">Phone</div>
                <div class="info-value">
                  <i class="fas fa-phone"></i> <?php echo e($profile->phone); ?>

                </div>
              </div>
              <?php endif; ?>

              <?php if($profile->email): ?>
              <div class="info-item">
                <div class="info-label">Email</div>
                <div class="info-value">
                  <i class="fas fa-envelope"></i> <?php echo e($profile->email); ?>

                </div>
              </div>
              <?php endif; ?>

              <div class="info-item">
                <div class="info-label">Created</div>
                <div class="info-value"><?php echo e($profile->created_at->format('M d, Y')); ?></div>
              </div>

              <div class="info-item">
                <div class="info-label">Profile URL</div>
                <div class="info-value">
                  <a href="<?php echo e(route('club.profile.public', $profile->profile_name)); ?>" target="_blank" style="color: #667eea;">
                    <i class="fas fa-link"></i> View Public Profile
                  </a>
                </div>
              </div>
            </div>

            <?php if($profile->tags && count($profile->tags) > 0): ?>
            <div class="info-item">
              <div class="info-label">Tags</div>
              <div class="tags-container">
                <?php $__currentLoopData = $profile->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <span class="tag"><?php echo e($tag); ?></span>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </div>
            </div>
            <?php endif; ?>

            <div class="action-buttons">
              <a href="<?php echo e(route('dashboard.club-profile.edit', $profile->_id)); ?>" class="btn-primary-modern btn-modern">
                <i class="fas fa-edit"></i> Edit Profile
              </a>

              <a href="<?php echo e(route('club.profile.public', $profile->profile_name)); ?>" target="_blank" class="btn-success-modern btn-modern">
                <i class="fas fa-eye"></i> View Public
              </a>

              <button onclick="deleteProfile('<?php echo e($profile->_id); ?>')" class="btn-secondary-modern btn-modern">
                <i class="fas fa-trash"></i> Delete Profile
              </button>
            </div>
          </div>
        </div>
      <?php endif; ?>

    </div>
  </section>

  <!-- Footer -->
  <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
function deleteProfile(profileId) {
    Swal.fire({
        title: 'Delete Club Profile?',
        text: 'This action cannot be undone. Your profile will be permanently removed.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, Delete',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/dashboard/club-profile/${profileId}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    Swal.fire({
                        title: 'Deleted!',
                        text: 'Your club profile has been deleted.',
                        icon: 'success',
                        confirmButtonColor: '#28a745'
                    }).then(() => {
                        window.location.reload();
                    });
                },
                error: function(xhr) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Failed to delete profile. Please try again.',
                        icon: 'error',
                        confirmButtonColor: '#dc3545'
                    });
                }
            });
        }
    });
}
</script>

</body>
</html>
<?php /**PATH /var/www/club/resources/views/dashboard/club-profile/index.blade.php ENDPATH**/ ?>