<!DOCTYPE html>
<html data-wf-page="66b8b8b8b8b8b8b8b8b8b8b8" data-wf-site="66b8b8b8b8b8b8b8b8b8b8b8" lang="en">
<head>
    <meta charset="utf-8">
    <title>Create Club Profile - EVOLUTION888 business center</title>
    <meta content="Create your club profile" name="description">
    <meta content="Create Club Profile - EVOLUTION888 business center" property="og:title">
    <meta content="Create your club profile" property="og:description">
    <meta content="Create Club Profile - EVOLUTION888 business center" property="twitter:title">
    <meta content="Create your club profile" property="twitter:description">
    <meta property="og:type" content="website">
    <meta content="summary_large_image" name="twitter:card">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link href="/css/techbetatemplates.webflow.14600f26e.css" rel="stylesheet" type="text/css">
    <script src="/js/webflow.3de03aa26.js" type="text/javascript"></script>
    <style>
        .form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .form-field {
            margin-bottom: 20px;
        }
        .form-field.full-width {
            grid-column: 1 / -1;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        .form-help {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body class="body">
    <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
    <div class="section">
        <div class="container">
            <div class="form-container">
                <div style="text-align: center; margin-bottom: 40px;">
                    <h2>Create Club Profile</h2>
                    <p>Create your professional club profile to connect with other members</p>
                    <a href="<?php echo e(route('dashboard.club-profile.index')); ?>" style="color: #007bff; text-decoration: none;">
                        <div>← Back to Profile</div>
                    </a>
                </div>

                <form id="createClubProfileForm" method="POST" action="<?php echo e(route('dashboard.club-profile.store')); ?>" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    
                    <div class="form-grid">
                        <div class="form-field">
                            <label class="form-label">Display Name *</label>
                            <input type="text" name="display_name" class="input w-input" placeholder="Your full name" required>
                        </div>
                        
                        <div class="form-field">
                            <label class="form-label">Profile Name (URL) *</label>
                            <input type="text" name="profile_name" class="input w-input" placeholder="your-profile-name" required>
                            <div class="form-help">This will be your profile URL: /profile/your-profile-name</div>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-field">
                            <label class="form-label">Company</label>
                            <input type="text" name="company" class="input w-input" placeholder="Your company">
                        </div>
                        
                        <div class="form-field">
                            <label class="form-label">Position</label>
                            <input type="text" name="position" class="input w-input" placeholder="Your position">
                        </div>
                    </div>

                    <div class="form-field full-width">
                        <label class="form-label">Bio</label>
                        <textarea name="bio" class="input w-input" placeholder="Tell us about yourself..." rows="4"></textarea>
                    </div>

                    <div class="form-grid">
                        <div class="form-field">
                            <label class="form-label">Website</label>
                            <input type="url" name="website" class="input w-input" placeholder="https://yourwebsite.com">
                        </div>
                        
                        <div class="form-field">
                            <label class="form-label">Location</label>
                            <input type="text" name="location" class="input w-input" placeholder="City, Country">
                        </div>
                    </div>

                    <div class="form-field">
                        <label class="form-label">Avatar</label>
                        <input type="file" name="avatar" class="input w-input" accept="image/*">
                        <div class="form-help">Upload a profile picture (max 2MB)</div>
                    </div>

                    <div class="form-field">
                        <label class="form-label">Social Media Links</label>
                        <textarea name="social_links" class="input w-input" placeholder="One link per line" rows="3"></textarea>
                        <div class="form-help">Add your social media profiles, one per line</div>
                    </div>

                    <div class="form-field">
                        <label class="form-label">Tags</label>
                        <input type="text" name="tags" class="input w-input" placeholder="business, technology, innovation">
                        <div class="form-help">Add relevant tags to help people find your profile</div>
                    </div>

                    <div class="form-field">
                        <label style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" name="is_public" value="1" checked>
                            <span>Make profile public</span>
                        </label>
                        <div class="form-help">Allow other members to find and view your profile</div>
                    </div>

                    <div class="button-group">
                        <button type="submit" class="primary-button dark-mode w-inline-block">
                            <div class="text-block">Create Club Profile</div>
                        </button>
                        <a href="<?php echo e(route('dashboard.club-profile.index')); ?>" class="primary-button w-inline-block" style="background-color: #6c757d;">
                            <div class="text-block">Cancel</div>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <script src="/js/jquery-3.5.1.min.dc5e7f18c8.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function() {
            $('#createClubProfileForm').on('submit', function(e) {
                e.preventDefault();
                
                var formData = new FormData(this);
                
                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        Swal.fire({
                            title: 'Success!',
                            text: 'Club profile created successfully!',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                window.location.href = '<?php echo e(route("dashboard.club-profile.index")); ?>';
                            }
                        });
                    },
                    error: function(xhr) {
                        var errors = xhr.responseJSON.errors || {};
                        var errorMessage = 'Please fix the following errors:\n';
                        
                        if (xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        } else {
                            for (var field in errors) {
                                errorMessage += '- ' + errors[field][0] + '\n';
                            }
                        }
                        
                        Swal.fire({
                            title: 'Error!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/dashboard/club-profile/create.blade.php ENDPATH**/ ?>