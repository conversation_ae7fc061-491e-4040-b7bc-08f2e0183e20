<?php $__env->startSection('title'); ?>
Pending Club Profiles - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <style>
        .profile-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
            transition: box-shadow 0.3s ease;
        }
        .profile-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .profile-banner {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px 8px 0 0;
        }
        .profile-banner-placeholder {
            width: 100%;
            height: 150px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px 8px 0 0;
        }
        .tag-item {
            display: inline-block;
            background: #e9ecef;
            padding: 2px 8px;
            margin: 2px;
            border-radius: 12px;
            font-size: 12px;
            color: #495057;
        }
        .moderation-actions {
            border-top: 1px solid #dee2e6;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 0 0 8px 8px;
        }
        .user-info {
            font-size: 13px;
            color: #6c757d;
        }
        .profile-stats {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<!-- Page Title Area Start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Pending Club Profiles</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.club-profiles.index')); ?>">Club Profiles</a></li>
                    <li><span>Pending</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- Page Title Area End -->

<div class="main-content-inner">
    <div class="row">
        <!-- Stats Cards -->
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12 mt-5">
            <div class="card">
                <div class="seo-fact sbg1">
                    <div class="p-4 d-flex justify-content-between align-items-center">
                        <div class="seofct-icon"><i class="fa fa-clock-o"></i> Pending</div>
                        <h2><?php echo e($pendingCount); ?></h2>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12 mt-5">
            <div class="card">
                <div class="seo-fact sbg2">
                    <div class="p-4 d-flex justify-content-between align-items-center">
                        <div class="seofct-icon"><i class="fa fa-check"></i> Today Approved</div>
                        <h2><?php echo e($todayApproved); ?></h2>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12 mt-5">
            <div class="card">
                <div class="seo-fact sbg3">
                    <div class="p-4 d-flex justify-content-between align-items-center">
                        <div class="seofct-icon"><i class="fa fa-times"></i> Today Rejected</div>
                        <h2><?php echo e($todayRejected); ?></h2>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12 mt-5">
            <div class="card">
                <div class="seo-fact sbg4">
                    <div class="p-4 d-flex justify-content-between align-items-center">
                        <div class="seofct-icon"><i class="fa fa-users"></i> Total Profiles</div>
                        <h2><?php echo e($totalProfiles); ?></h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Profiles -->
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title float-left">Pending Club Profiles (<?php echo e($profiles->count()); ?>)</h4>
                    <p class="float-right mb-2">
                        <a class="btn btn-primary text-white" href="<?php echo e(route('admin.club-profiles.index')); ?>">
                            <i class="fa fa-list"></i> All Profiles
                        </a>
                    </p>
                    <div class="clearfix"></div>
                    
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    
                    <?php if($profiles->count() > 0): ?>
                        <div class="row">
                            <?php $__currentLoopData = $profiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $profile): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-6 col-xl-4">
                                <div class="profile-card">
                                    <!-- Banner -->
                                    <?php if($profile->banner_image): ?>
                                        <img src="<?php echo e(Storage::url($profile->banner_image)); ?>" alt="Banner" class="profile-banner">
                                    <?php else: ?>
                                        <div class="profile-banner-placeholder">
                                            <i class="fa fa-image fa-3x text-white"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Content -->
                                    <div class="p-3">
                                        <h5 class="mb-2">
                                            <strong><?php echo e($profile->profile_name); ?></strong>
                                            <small class="text-muted float-right">
                                                <?php echo e($profile->created_at->diffForHumans()); ?>

                                            </small>
                                        </h5>
                                        
                                        <?php if($profile->description): ?>
                                            <p class="text-muted small mb-2"><?php echo e(Str::limit($profile->description, 100)); ?></p>
                                        <?php endif; ?>
                                        
                                        <!-- User Info -->
                                        <div class="user-info mb-2">
                                            <i class="fa fa-user"></i> <?php echo e($profile->user->name ?? 'N/A'); ?>

                                            <br><i class="fa fa-envelope"></i> <?php echo e($profile->user->email ?? 'N/A'); ?>

                                        </div>
                                        
                                        <!-- Tags -->
                                        <?php if($profile->tags && count($profile->tags) > 0): ?>
                                            <div class="mb-2">
                                                <?php $__currentLoopData = array_slice($profile->tags, 0, 5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <span class="tag-item"><?php echo e($tag); ?></span>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php if(count($profile->tags) > 5): ?>
                                                    <span class="tag-item">+<?php echo e(count($profile->tags) - 5); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <!-- Stats -->
                                        <div class="profile-stats">
                                            <span><i class="fa fa-eye"></i> <?php echo e(number_format($profile->views_count)); ?> views</span>
                                            <span><i class="fa fa-calendar"></i> <?php echo e($profile->created_at->format('M d, Y')); ?></span>
                                        </div>
                                    </div>
                                    
                                    <!-- Actions -->
                                    <div class="moderation-actions">
                                        <div class="row">
                                            <div class="col-6">
                                                <button class="btn btn-success btn-sm btn-block approve-btn" data-id="<?php echo e($profile->_id); ?>">
                                                    <i class="fa fa-check"></i> Approve
                                                </button>
                                            </div>
                                            <div class="col-6">
                                                <button class="btn btn-danger btn-sm btn-block reject-btn" data-id="<?php echo e($profile->_id); ?>">
                                                    <i class="fa fa-times"></i> Reject
                                                </button>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-6">
                                                <a href="<?php echo e(route('admin.club-profiles.show', $profile->_id)); ?>" class="btn btn-info btn-sm btn-block">
                                                    <i class="fa fa-eye"></i> View Details
                                                </a>
                                            </div>
                                            <div class="col-6">
                                                <a href="<?php echo e($profile->public_url); ?>" target="_blank" class="btn btn-outline-primary btn-sm btn-block">
                                                    <i class="fa fa-external-link"></i> Preview
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        
                        <!-- Pagination -->
                        <div class="mt-4">
                            <?php echo e($profiles->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fa fa-check-circle fa-3x text-success mb-3"></i>
                            <h4>No Pending Profiles</h4>
                            <p class="text-muted">All club profiles have been moderated!</p>
                            <a href="<?php echo e(route('admin.club-profiles.index')); ?>" class="btn btn-primary">
                                <i class="fa fa-list"></i> View All Profiles
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Moderation Modal -->
<div class="modal fade" id="moderationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Moderate Club Profile</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="moderationForm">
                    <input type="hidden" id="profileId">
                    <input type="hidden" id="action">
                    <div class="form-group">
                        <label>Notes</label>
                        <textarea class="form-control" id="notes" rows="3" placeholder="Add moderation notes..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmModeration">Confirm</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Approve button
            $('.approve-btn').click(function() {
                const profileId = $(this).data('id');
                $('#profileId').val(profileId);
                $('#action').val('approve');
                $('.modal-title').text('Approve Club Profile');
                $('#notes').attr('placeholder', 'Add approval notes (optional)...');
                $('#moderationModal').modal('show');
            });

            // Reject button
            $('.reject-btn').click(function() {
                const profileId = $(this).data('id');
                $('#profileId').val(profileId);
                $('#action').val('reject');
                $('.modal-title').text('Reject Club Profile');
                $('#notes').attr('placeholder', 'Add rejection reason (required)...');
                $('#moderationModal').modal('show');
            });

            // Confirm moderation
            $('#confirmModeration').click(function() {
                const profileId = $('#profileId').val();
                const action = $('#action').val();
                const notes = $('#notes').val();
                
                if (action === 'reject' && !notes.trim()) {
                    alert('Rejection reason is required');
                    return;
                }
                
                $.post(`/admin/club-profiles/${profileId}/${action}`, {
                    _token: '<?php echo e(csrf_token()); ?>',
                    notes: notes
                }).done(function(response) {
                    if (response.success) {
                        location.reload();
                    }
                }).fail(function() {
                    alert('An error occurred. Please try again.');
                });
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/club-profiles/pending.blade.php ENDPATH**/ ?>