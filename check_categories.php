<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Categories ===\n";
$categories = \App\Models\Category::all();
foreach ($categories as $category) {
    echo "ID: {$category->_id}, Name: {$category->category_name}, Percentage: {$category->percentage}%\n";
}

echo "\n=== Levels ===\n";
$levels = \App\Models\Level::orderBy('level')->get();
foreach ($levels as $level) {
    echo "Level: {$level->level}, Percentage: {$level->percentage}%\n";
}

echo "\n=== Check if subscription category exists ===\n";
$subscriptionCategory = \App\Models\Category::where('category_name', 'subscription')->first();
if ($subscriptionCategory) {
    echo "Subscription category exists: {$subscriptionCategory->percentage}%\n";
} else {
    echo "Subscription category does not exist\n";
}
