<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Creating subscription category...\n";

// Check if subscription category already exists
$existingCategory = \App\Models\Category::where('category_name', 'subscription')->first();
if ($existingCategory) {
    echo "Subscription category already exists with {$existingCategory->percentage}% percentage\n";
} else {
    // Create subscription category with 20% percentage
    $category = \App\Models\Category::create([
        'category_name' => 'subscription',
        'percentage' => 20
    ]);
    
    echo "Subscription category created successfully with ID: {$category->_id}\n";
    echo "Category name: {$category->category_name}\n";
    echo "Percentage: {$category->percentage}%\n";
}

echo "Done!\n";
