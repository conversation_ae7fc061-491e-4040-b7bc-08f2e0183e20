<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\KYCController;
use App\Http\Controllers\Dashboard\DashboardController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\UsersController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\LevelController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\ProductStorageController;
use App\Http\Controllers\Admin\CodeController;
use App\Http\Controllers\Admin\PartnersController;
use App\Http\Controllers\Admin\NewsController;
use App\Http\Controllers\Admin\ProjectsController;
use App\Http\Controllers\Admin\SocialProjectsController;
use App\Http\Controllers\Admin\KycAdminController;
use App\Http\Controllers\Admin\PaymentMethodController;
use App\Http\Controllers\Admin\WithdrawController;
use App\Http\Controllers\ViewController;
use App\Http\Controllers\System\ApiController;
use App\Http\Controllers\Shop\ShopController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Cookie;
use App\Http\Controllers\Partner\PartnerPaymentController;
use App\Http\Controllers\Staking\StakingController;
use App\Http\Controllers\BusinessCardController;
use App\Http\Controllers\ClubProfileController;

use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\Admin\BusinessCardAdminController;
use App\Http\Controllers\Admin\ClubProfileAdminController;
use App\Http\Controllers\Admin\SubscriptionPlanAdminController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
| Здесь размещён полный список маршрутов вашего приложения,
| включая логику авторизации (login по коду, 2FA), админку, SSO и т.д.
|--------------------------------------------------------------------------
*/

// Главная страница
Route::get('/', [ViewController::class, 'index'])->name('index');
Route::get('/about', [ViewController::class, 'about'])->name('about');

Route::get('/contact', [ViewController::class, 'contact'])->name('contact');
Route::post('/contact/send', [ViewController::class, 'contact_send'])->name('contact_send');

Route::get('/partners', [ViewController::class, 'partners'])->name('partners');
Route::get('/barabulka', [ViewController::class, 'barabulka'])->name('barabulka');
Route::get('/construction', [ViewController::class, 'construction'])->name('construction');

Route::get('/affiliate', [ViewController::class, 'affiliate'])->name('affiliate');
Route::get('/publicoffer', [ViewController::class, 'publicoffer'])->name('publicoffer');
Route::get('/disclaimer', [ViewController::class, 'disclaimer'])->name('disclaimer');
Route::get('/documentation', [ViewController::class, 'documentation'])->name('documentation');
Route::get('/tokenomics', [ViewController::class, 'tokenomics'])->name('tokenomics');
Route::get('/whitepaper', [ViewController::class, 'whitepaper'])->name('whitepaper');
Route::get('/presentation', [ViewController::class, 'presentation'])->name('presentation');
Route::get('/integration', [ViewController::class, 'integration'])->name('integration');
Route::get('/partners', [ViewController::class, 'partners'])->name('partners');
Route::get('/instructions', [ViewController::class, 'instructions'])->name('instructions');
Route::get('/exchange', [ViewController::class, 'exchange'])->name('exchange');
Route::get('/policy', [ViewController::class, 'policy'])->name('policy');
Route::get('/marketing', [ViewController::class, 'marketing'])->name('marketing');

Route::get('/news_list', [ViewController::class, 'news_list'])->name('news_list');
Route::get('/news_view', [ViewController::class, 'news_view'])->name('news_view');
Route::get('/news_search', [ViewController::class, 'news_search'])->name('news_search');

// Сохранение реферального кода в Cookie
Route::get('/referral/{code}', function ($code) {
    return redirect('/?ref=' . $code);
});

// --- Гостевые маршруты (только для неавторизованных) ---
Route::middleware('guest')->group(function () {
    Route::get('/2fa/verify', [AuthController::class, 'showVerifyForm'])->name('2fa.verify');
    Route::post('/2fa/verify', [AuthController::class, 'verify2FA'])->name('2fa.verify.post');
    Route::get('/login', [AuthController::class, 'login_view'])->name('login');
    Route::post('/login/request-code', [AuthController::class, 'requestCode'])->name('login.request_code');
    Route::post('/login/verify-code', [AuthController::class, 'verifyCode'])->name('login.verify_code');
});

// --- Маршруты для авторизованных пользователей ---
Route::middleware('auth')->group(function () {
	#SSO
	Route::get('/sso/authorize', [AuthController::class, 'ssoAuthorize'])->name('sso.authorize');
	Route::post('/sso/confirm', [AuthController::class, 'ssoConfirm'])->name('sso.confirm');
	#SSO	
	Route::get('/pay', [PartnerPaymentController::class, 'showPaymentPage'])->name('partner.pay');
	Route::post('/pay/confirm', [PartnerPaymentController::class, 'confirmPayment'])->name('partner.pay.confirm');	
	Route::get('/project_list', [ViewController::class, 'project_list'])->name('project_list');
	Route::get('/social_projects_list', [ViewController::class, 'social_projects_list'])->name('social_projects_list');
	Route::get('/project_view', [ViewController::class, 'project_view'])->name('project_view');
	Route::get('/social_project_view', [ViewController::class, 'social_projects_view'])->name('social_projects_view');
	Route::get('/projects_search', [ViewController::class, 'projects_search'])->name('projects_search');
	Route::get('/social_projects_search', [ViewController::class, 'social_projects_search'])->name('social_projects_search');


    Route::get('/profile', [DashboardController::class, 'profile'])->name('profile');
    Route::get('/shop', [ShopController::class, 'shop_view'])->name('shop_view');
    Route::get('/product_view', [ShopController::class, 'product_view'])->name('product_view');

    Route::get('/2fa/activate', [AuthController::class, 'activate2FA'])->name('2fa.activate');
    Route::post('/2fa/activate', [AuthController::class, 'verify2FASetup'])->name('2fa.activate.submit');
    Route::post('/2fa/deactivate', [AuthController::class, 'deactivate2FA'])->name('2fa.deactivate');

    Route::get('/dashboard', [DashboardController::class, 'index'])->middleware('can:user.dashboard.view')->name('dashboard');

    // User Business Cards and Club Profiles
    Route::prefix('dashboard')->middleware('auth')->group(function () {
        Route::get('/business-cards', [BusinessCardController::class, 'userIndex'])->name('dashboard.business-cards.index');
        Route::get('/business-cards/create', [BusinessCardController::class, 'userCreate'])->name('dashboard.business-cards.create');
        Route::post('/business-cards', [BusinessCardController::class, 'userStore'])->name('dashboard.business-cards.store');
        Route::get('/business-cards/{businessCard}/edit', [BusinessCardController::class, 'userEdit'])->name('dashboard.business-cards.edit');
        Route::put('/business-cards/{businessCard}', [BusinessCardController::class, 'userUpdate'])->name('dashboard.business-cards.update');
        Route::delete('/business-cards/{businessCard}', [BusinessCardController::class, 'userDestroy'])->name('dashboard.business-cards.destroy');

        Route::get('/club-profile', [ClubProfileController::class, 'userIndex'])->name('dashboard.club-profile.index');
        Route::get('/club-profile/create', [ClubProfileController::class, 'userCreate'])->name('dashboard.club-profile.create');
        Route::post('/club-profile', [ClubProfileController::class, 'userStore'])->name('dashboard.club-profile.store');
        Route::get('/club-profile/{clubProfile}/edit', [ClubProfileController::class, 'userEdit'])->name('dashboard.club-profile.edit');
        Route::put('/club-profile/{clubProfile}', [ClubProfileController::class, 'userUpdate'])->name('dashboard.club-profile.update');
        Route::delete('/club-profile/{clubProfile}', [ClubProfileController::class, 'userDestroy'])->name('dashboard.club-profile.destroy');
    });

    Route::prefix('api')->group(function () {
		Route::get('/get_stacking_rates', [StakingController::class, 'getRates']);
		Route::get('/get_user_stakings', [StakingController::class, 'getUserStakings']);
		Route::post('/staking', [StakingController::class, 'stake']);
        Route::post('/transfer', [DashboardController::class, 'transfer']);
		Route::post('/withdraw', [DashboardController::class, 'createWithdrawalRequest']);
        Route::post('/update_profile', [DashboardController::class, 'update_profile']);
        Route::get('/get_purchases', [DashboardController::class, 'get_purchases']);
		Route::get('/get_user_info', [DashboardController::class, 'get_user_info']);
		Route::get('/payment-methods', [DashboardController::class, 'paymentmethods']);
        Route::get('/get_chart_info', [DashboardController::class, 'get_chart_info']);
		Route::get('/get_referal_tree', [DashboardController::class, 'get_referal_tree']);
        Route::get('/get_transactions', [DashboardController::class, 'get_transactions']);
        Route::get('/products_search', [ShopController::class, 'products_search']);
        Route::post('/product/buy', [ShopController::class, 'product_buy']);
        Route::post('/code/redeem', [DashboardController::class, 'redeemCode']);
		Route::get('/kyc/status', [KYCController::class, 'checkStatus']);
		Route::post('/kyc/upload', [KYCController::class, 'uploadFile']);
		Route::post('/kyc/submit', [KYCController::class, 'submitApplication']);
    });
});
Route::middleware('auth')->prefix('admin')->name('admin.')->group(function () {

    Route::get('/', [AdminController::class, 'index'])
         ->middleware('can:admin.dashboard.view')
         ->name('index');

    Route::resource('role', RoleController::class);
    Route::resource('users', UsersController::class);
    Route::resource('categories', CategoryController::class);
    Route::resource('levels', LevelController::class);
    Route::resource('products', ProductController::class);
    Route::resource('products_storage', ProductStorageController::class);
    Route::resource('codes', CodeController::class);
	Route::resource('partners', PartnersController::class);
	Route::resource('news', NewsController::class);	
	Route::resource('projects', ProjectsController::class);	
	Route::resource('social_projects', SocialProjectsController::class);	
	Route::resource('kyc', KycAdminController::class);	
	Route::resource('payment_methods', PaymentMethodController::class);
	Route::resource('withdrawals', WithdrawController::class);



	// Subscription Plans Management (Admin) - New System
	Route::prefix('subscription-plans')->name('subscription-plans.')->middleware('role:admin')->group(function () {
		Route::get('/', [SubscriptionPlanAdminController::class, 'index'])->name('index');
		Route::get('/create', [SubscriptionPlanAdminController::class, 'create'])->name('create');
		Route::post('/', [SubscriptionPlanAdminController::class, 'store'])->name('store');
		Route::get('/{id}/edit', [SubscriptionPlanAdminController::class, 'edit'])->name('edit');
		Route::put('/{id}', [SubscriptionPlanAdminController::class, 'update'])->name('update');
		Route::delete('/{id}', [SubscriptionPlanAdminController::class, 'destroy'])->name('destroy');
		Route::post('/{id}/toggle-status', [SubscriptionPlanAdminController::class, 'toggleStatus'])->name('toggle-status');
		Route::get('/{id}/statistics', [SubscriptionPlanAdminController::class, 'statistics'])->name('statistics');
		Route::post('/seed-defaults', [SubscriptionPlanAdminController::class, 'seedDefaults'])->name('seed-defaults');
	});

	// Business Cards Management (Admin)
	Route::prefix('business-cards')->name('business-cards.')->middleware('role:admin|moderator')->group(function () {
		Route::get('/', [BusinessCardAdminController::class, 'index'])->name('index');
		Route::get('/pending', [BusinessCardAdminController::class, 'pending'])->name('pending');
		Route::get('/{id}', [BusinessCardAdminController::class, 'show'])->name('show');
		Route::get('/{id}/edit', [BusinessCardAdminController::class, 'edit'])->name('edit');
		Route::put('/{id}', [BusinessCardAdminController::class, 'update'])->name('update');
		Route::delete('/{id}', [BusinessCardAdminController::class, 'destroy'])->name('destroy');
		Route::post('/{id}/approve', [BusinessCardAdminController::class, 'approve'])->name('approve');
		Route::post('/{id}/reject', [BusinessCardAdminController::class, 'reject'])->name('reject');
		Route::post('/{id}/toggle-active', [BusinessCardAdminController::class, 'toggleActive'])->name('toggle-active');
	});

	// Club Profile Management (Admin)
	Route::prefix('club-profiles')->name('club-profiles.')->middleware('role:admin|moderator')->group(function () {
		Route::get('/', [ClubProfileAdminController::class, 'index'])->name('index');
		Route::get('/pending', [ClubProfileAdminController::class, 'pending'])->name('pending');
		Route::get('/{id}', [ClubProfileAdminController::class, 'show'])->name('show');
		Route::get('/{id}/edit', [ClubProfileAdminController::class, 'edit'])->name('edit');
		Route::put('/{id}', [ClubProfileAdminController::class, 'update'])->name('update');
		Route::delete('/{id}', [ClubProfileAdminController::class, 'destroy'])->name('destroy');
		Route::post('/{id}/approve', [ClubProfileAdminController::class, 'approve'])->name('approve');
		Route::post('/{id}/reject', [ClubProfileAdminController::class, 'reject'])->name('reject');
		Route::post('/{id}/toggle-active', [ClubProfileAdminController::class, 'toggleActive'])->name('toggle-active');
	});
});
Route::prefix('api')->group(function () {
    Route::get('/logout', [AuthController::class, 'logout'])->name('logout');
});
Route::prefix('api/v1')->group(function () {
    Route::post('/distributeReward', [ApiController::class, 'distributeReward']);
});

// Business Cards Routes (Public access for viewing)
Route::get('/tag/{profile_name}', [BusinessCardController::class, 'show'])->name('business-cards.public');

// Club Profiles Routes (Public access for viewing)
Route::get('/profile/{profile_name}', [ClubProfileController::class, 'show'])->name('club.profile.public');
Route::get('/club_members', [ClubProfileController::class, 'index'])->name('club.members');

// Test subscription page
Route::get('/test-subscription', function () {
    return view('test-subscription');
});

// Authenticated routes for business cards and club profiles
Route::middleware('auth')->group(function () {



    // Subscription Management (New System)
    Route::prefix('subscriptions')->name('subscriptions.')->group(function () {
        Route::get('/plans', [SubscriptionController::class, 'plans'])->name('plans');
        Route::get('/purchase/{planId}', [SubscriptionController::class, 'purchase'])->name('purchase');
        Route::post('/purchase/{planId}', [SubscriptionController::class, 'processPurchase'])->name('purchase.process');

        Route::post('/cancel', [SubscriptionController::class, 'cancel'])->name('cancel');
        Route::get('/status', [SubscriptionController::class, 'status'])->name('status');
    });

    // Business Cards Management (requires active subscription)
    Route::middleware('subscription.required')->prefix('business-cards')->name('business-cards.')->group(function () {
        Route::get('/', [BusinessCardController::class, 'index'])->name('index');
        Route::get('/create', [BusinessCardController::class, 'create'])->name('create');
        Route::post('/', [BusinessCardController::class, 'store'])->name('store');
        Route::get('/{id}/edit', [BusinessCardController::class, 'edit'])->name('edit');
        Route::put('/{id}', [BusinessCardController::class, 'update'])->name('update');
        Route::delete('/{id}', [BusinessCardController::class, 'destroy'])->name('destroy');
        Route::post('/{id}/toggle-active', [BusinessCardController::class, 'toggleActive'])->name('toggle-active');
    });

    // Club Profile Management (requires active subscription)
    Route::middleware('subscription.required')->prefix('club')->name('club.')->group(function () {
        Route::get('/manage', [ClubProfileController::class, 'manage'])->name('manage');
        Route::post('/profile', [ClubProfileController::class, 'store'])->name('profile.store');
        Route::get('/profile/data', [ClubProfileController::class, 'getProfileData'])->name('profile.data');
        Route::post('/profile/toggle-active', [ClubProfileController::class, 'toggleActive'])->name('profile.toggle-active');
        Route::delete('/profile', [ClubProfileController::class, 'destroy'])->name('profile.destroy');
        Route::post('/check-profile-name', [ClubProfileController::class, 'checkProfileName'])->name('check-profile-name');
    });

});