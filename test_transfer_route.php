<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing Transfer Route ===\n";

// Check if route exists
$routes = \Illuminate\Support\Facades\Route::getRoutes();
$transferRoute = null;

foreach ($routes as $route) {
    if ($route->uri() === 'api/transfer' && in_array('POST', $route->methods())) {
        $transferRoute = $route;
        break;
    }
}

if ($transferRoute) {
    echo "✓ Transfer route found: POST /api/transfer\n";
    echo "Controller: " . $transferRoute->getActionName() . "\n";
    echo "Middleware: " . implode(', ', $transferRoute->middleware()) . "\n";
} else {
    echo "✗ Transfer route NOT found\n";
}

// Check if DashboardController has transfer method
echo "\n=== Checking DashboardController ===\n";
$controller = new \App\Http\Controllers\Dashboard\DashboardController();
if (method_exists($controller, 'transfer')) {
    echo "✓ transfer method exists in DashboardController\n";
} else {
    echo "✗ transfer method NOT found in DashboardController\n";
}

// Check if User model has balance field
echo "\n=== Checking User Model ===\n";
$user = \App\Models\User::first();
if ($user) {
    echo "✓ User found with ID: {$user->_id}\n";
    echo "Balance: " . ($user->balance ?? 'NULL') . "\n";
    echo "Email: {$user->email}\n";
} else {
    echo "✗ No users found\n";
}

echo "\nDone!\n";
